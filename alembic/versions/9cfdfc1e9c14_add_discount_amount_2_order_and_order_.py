"""add_discount_amount_2_order_and_order_items

Revision ID: 9cfdfc1e9c14
Revises: 0d1194a81d6f
Create Date: 2025-09-18 15:45:47.828047

"""
from typing import Sequence, Union

from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import mysql

# revision identifiers, used by Alembic.
revision: str = '9cfdfc1e9c14'
down_revision: Union[str, None] = '0d1194a81d6f'
branch_labels: Union[str, Sequence[str], None] = None
depends_on: Union[str, Sequence[str], None] = None


def upgrade() -> None:
    """Upgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.add_column('order_items', sa.Column('discount_amount', sa.Float(), nullable=False, comment='优惠金额'))
    op.alter_column('order_items', 'status',
               existing_type=mysql.ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PARTIAL_PAID', collation='utf8mb4_unicode_ci'),
               type_=sa.Enum('PENDING', 'PAID', 'CANCELLED', 'COMPLETED', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PARTIAL_PAID', name='orderstatus'),
               existing_comment='订单状态',
               existing_nullable=False)
    op.add_column('orders', sa.Column('discount_amount', sa.Float(), nullable=False, comment='优惠金额'))
    # ### end Alembic commands ###


def downgrade() -> None:
    """Downgrade schema."""
    # ### commands auto generated by Alembic - please adjust! ###
    op.drop_column('orders', 'discount_amount')
    op.alter_column('order_items', 'status',
               existing_type=sa.Enum('PENDING', 'PAID', 'CANCELLED', 'COMPLETED', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PARTIAL_PAID', name='orderstatus'),
               type_=mysql.ENUM('PENDING', 'PAID', 'SHIPPED', 'DELIVERED', 'VERIFIED', 'COMPLETED', 'CANCELLED', 'REFUNDED', 'REFUNDED_PARTIAL', 'PARTIAL_PAID', collation='utf8mb4_unicode_ci'),
               existing_comment='订单状态',
               existing_nullable=False)
    op.drop_column('order_items', 'discount_amount')
    # ### end Alembic commands ###
