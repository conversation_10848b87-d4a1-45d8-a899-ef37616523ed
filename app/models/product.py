import enum
from datetime import datetime

from sqlalchemy import Column, Integer, String, Float, Boolean, ForeignKey, Text, Enum, DateTime, Table, \
    UniqueConstraint
from sqlalchemy.orm import relationship

from app.db.base_class import Base
from app.models.content import product_content_relation
from app.models.enum import Status
from app.models.pricing import product_pricing_strategy_relation
from app.models.rule import product_rule_relation
from app.models.tag import product_tag_relation
from app.utils.common import get_current_time

# 产品与产品分类关联表
product_category_relation = Table(
    'product_category_relations',
    Base.metadata,
    Column('product_id', Integer, ForeignKey('products.id')),
    Column('category_id', Integer, ForeignKey('product_categories.id')),
    UniqueConstraint('product_id', 'category_id', name='uq_product_category')
)


class ProductType(enum.Enum):
    PRODUCT = "product"  # 普通产品
    DIRECT_SALE = "direct_sale"  # 直销产品
    RESERVATION = "reservation"  # 预订产品
    VIRTUAL = "virtual"  # 虚拟产品


class MealType(enum.Enum):
    BUSINESS = "business"  # 商务餐
    BUFFET = "buffet"  # 自助餐
    COUPON = "coupon"  # 优惠券

class ObjectType(enum.Enum):
    COUPON_BATCH = "coupon_batch"  # 优惠券批次


class Product(Base):
    """产品"""
    __tablename__ = "products"

    id = Column(Integer, primary_key=True, index=True, comment="商品ID")
    name = Column(String(100), comment="商品名称")
    price = Column(Float, default=0, comment="基本价格")
    description = Column(Text, comment="商品描述")
    image = Column(String(255), nullable=True, comment="产品主图")
    stock = Column(Integer, default=0, comment="库存数量")
    status = Column(Enum(Status), default=Status.ACTIVE, comment="商品状态")
    type = Column(Enum(ProductType, name='producttype'), default=ProductType.PRODUCT, comment="产品类型")
    meal_type = Column(Enum(MealType), default=MealType.BUFFET, comment="餐食类型：商务餐/自助餐/现场点餐")
    listed_at = Column(DateTime, default=datetime.now, comment="上架时间")
    created_at = Column(DateTime, default=datetime.now)
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now)

    # 关联
    contents = relationship("Content", secondary=product_content_relation, back_populates="products")
    tags = relationship("Tag", secondary=product_tag_relation, back_populates="products")
    rules = relationship("Rule", secondary=product_rule_relation, back_populates="products")
    pricing_strategies = relationship("PricingStrategy", secondary=product_pricing_strategy_relation,
                                      back_populates="products")
    order_items = relationship("OrderItem", back_populates="product")
    reservation_requests = relationship("ReservationRequest", back_populates="product")
    menus = relationship("Menu", back_populates="product")
    categories = relationship("ProductCategory", secondary=product_category_relation, back_populates="products")
    gift_rule_condition_rels = relationship("OrdGiftRuleOrdProdRel", back_populates="order_product")
    gift_rule_result_rels = relationship("OrdGiftRuleGiftProdRel", back_populates="gift_product")
    bundle_strategy_rels = relationship("BundleStrategyProdRel", back_populates="bundle_product",
                                        cascade="all, delete-orphan")

    __mapper_args__ = {
        'polymorphic_on': type,
        'polymorphic_identity': ProductType.PRODUCT
    }


class DirectSaleProduct(Product):
    """直销产品"""
    __tablename__ = "direct_sale_products"

    id = Column(Integer, ForeignKey("products.id"), primary_key=True)
    shipping_fee = Column(Float, default=0, comment="运费")

    __mapper_args__ = {
        "polymorphic_identity": ProductType.DIRECT_SALE
    }


class ReservationProduct(Product):
    """预订产品"""
    __tablename__ = "reservation_products"

    id = Column(Integer, ForeignKey("products.id"), primary_key=True)
    reservation_fee = Column(Float, nullable=True, default=0)
    max_reservations = Column(Integer, nullable=True, default=0)
    reservation_deadline = Column(DateTime, nullable=True)
    cancellation_deadline = Column(DateTime, nullable=True)
    is_approval_required = Column(Boolean, nullable=True, default=False)

    __mapper_args__ = {
        "polymorphic_identity": ProductType.RESERVATION
    }

class VirtualProduct(Product):
    """虚拟产品"""
    __tablename__ = "virtual_products"

    id = Column(Integer, ForeignKey("products.id"), primary_key=True)
    object_id = Column(Integer, nullable=False, comment="对象ID")
    object_type = Column(Enum(ObjectType), nullable=False, comment="对象类型")
    __mapper_args__ = {
        "polymorphic_identity": ProductType.VIRTUAL
    }


class ProductCategory(Base):
    """产品分类"""
    __tablename__ = "product_categories"

    id = Column(Integer, primary_key=True, index=True, comment="分类ID")
    name = Column(String(50), nullable=False, comment="分类名称")
    description = Column(String(200), nullable=True, comment="分类描述")
    image = Column(String(255), nullable=True, comment="分类图片")
    sort_order = Column(Integer, default=0, comment="排序顺序")
    status = Column(Enum(Status), nullable=False, default=Status.ACTIVE, comment="分类状态")
    parent_id = Column(Integer, ForeignKey("product_categories.id"), nullable=True, comment="父分类ID")
    created_at = Column(DateTime, default=datetime.now, comment="创建时间")
    updated_at = Column(DateTime, default=datetime.now, onupdate=datetime.now, comment="更新时间")
    key = Column(String(50), default="", nullable=True, comment="分类键")

    # 关联
    products = relationship("Product", secondary=product_category_relation, back_populates="categories")
    parent = relationship("ProductCategory", remote_side=[id], backref="children")
