# 用户订单接口API返回结果模拟数据参考示例

## 1. 获取用户所有订单列表 - GET /user/orders

### 请求参数
- Header: `token: "your_user_token"`
- Query: `skip=0&limit=20`

### 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "total": 3,
        "orders": [
            {
                "id": 1001,
                "order_no": "ORD20241213001",
                "type": "order",
                "status": "paid",
                "payment_status": "paid",
                "total_amount": 158.0,
                "payable_amount": 138.0,
                "actual_amount_paid": 138.0,
                "payment_method": "wechat_pay",
                "created_at": "2024-12-13 10:30:00",
                "updated_at": "2024-12-13 10:35:00",
                "payment_time": "2024-12-13 10:35:00"
            },
            {
                "id": 1002,
                "order_no": "ORD20241213002",
                "type": "reservation",
                "status": "pending",
                "payment_status": "unpaid",
                "total_amount": 288.0,
                "payable_amount": 288.0,
                "actual_amount_paid": 0.0,
                "payment_method": "account_balance",
                "created_at": "2024-12-13 14:20:00",
                "updated_at": "2024-12-13 14:20:00",
                "payment_time": null
            },
            {
                "id": 1003,
                "order_no": "ORD20241213003",
                "type": "direct_sale",
                "status": "cancelled",
                "payment_status": "refunded",
                "total_amount": 99.0,
                "payable_amount": 99.0,
                "actual_amount_paid": 0.0,
                "payment_method": "wechat_pay",
                "created_at": "2024-12-13 16:45:00",
                "updated_at": "2024-12-13 17:00:00",
                "payment_time": null
            }
        ]
    }
}
```

### 错误响应示例
```json
{
    "code": 401,
    "message": "未登录"
}
```

## 2. 获取订单详情 - GET /user/orders/{order_id}

### 请求参数
- Header: `token: "your_user_token"`
- Path: `order_id=1001`

### 成功响应示例
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "id": 1001,
        "order_no": "ORD20241213001",
        "type": "order",
        "status": "paid",
        "payment_status": "paid",
        "total_amount": 158.0,
        "payable_amount": 138.0,
        "actual_amount_paid": 138.0,
        "payment_method": "wechat_pay",
        "created_at": "2024-12-13 10:30:00",
        "updated_at": "2024-12-13 10:35:00",
        "payment_time": "2024-12-13 10:35:00",
        "pricing_remark": "会员优惠",
        "items": [
            {
                "id": 2001,
                "product_id": 101,
                "product_name": "素食自助餐券",
                "quantity": 2,
                "price": 79.0,
                "subtotal": 158.0,
                "final_price": 69.0,
                "payable_amount": 138.0,
                "pricing_remark": "会员价"
            }
        ],
        "coupon_discounts": [
            {
                "coupon_usage_record_id": 3001,
                "coupon_id": 501,
                "coupon_name": "新用户20元优惠券",
                "discount_amount": 20.0,
                "used_at": "2024-12-13 10:35:00"
            }
        ],
        "total_discount_amount": 20.0
    }
}
```

### 错误响应示例
```json
{
    "code": 404,
    "message": "订单不存在"
}
```

```json
{
    "code": 403,
    "message": "无权访问此订单"
}
```

## 3. 取消订单 - POST /user/orders/{order_id}/cancel

### 请求参数
- Header: `token: "your_user_token"`
- Path: `order_id=1002`

### 成功响应示例
```json
{
    "code": 200,
    "message": "订单取消成功",
    "data": {
        "order_id": 1002,
        "order_no": "ORD20241213002",
        "status": "refunded",
        "payment_status": "refunded",
        "cancelled_at": "2024-12-13 18:30:00"
    }
}
```

### 错误响应示例
```json
{
    "code": 400,
    "message": "订单状态为completed，不允许取消"
}
```

```json
{
    "code": 404,
    "message": "订单不存在"
}
```

```json
{
    "code": 403,
    "message": "无权操作此订单"
}
```

## 字段说明

### 订单状态 (status)
- `pending`: 待处理
- `paid`: 已支付
- `shipped`: 已发货
- `delivered`: 已送达
- `completed`: 已完成
- `cancelled`: 已取消
- `refunded`: 已退款

### 支付状态 (payment_status)
- `unpaid`: 未支付
- `paid`: 已支付
- `refunded`: 已退款

### 订单类型 (type)
- `order`: 普通订单
- `direct_sale`: 直销订单
- `reservation`: 预订订单
- `recharge`: 充值订单

### 支付方式 (payment_method)
- `account_balance`: 账户余额
- `wechat_pay`: 微信支付
- `alipay`: 支付宝
- `cash`: 现金

## 接口路径
- 获取订单列表: `GET /api/v1/wechat-mini-app/user/orders`
- 获取订单详情: `GET /api/v1/wechat-mini-app/user/orders/{order_id}`
- 取消订单: `POST /api/v1/wechat-mini-app/user/orders/{order_id}/cancel`
