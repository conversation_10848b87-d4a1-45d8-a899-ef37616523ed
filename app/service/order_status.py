"""
订单状态服务模块

该模块提供订单状态检查服务，验证订单状态与订单项状态的一致性。
"""

from typing import List, Dict, Any, Tuple, Optional
from sqlalchemy.orm import Session
from pydantic import BaseModel

from app.models.order import Order, OrderItem, OrderStatus, OrderType
from app.dao.order import order_dao


class OrderStatusCheckResult(BaseModel):
    """订单状态检查结果"""
    is_valid: bool
    order_id: int
    order_type: OrderType
    order_status: OrderStatus
    order_items: List[Dict[str, Any]]


class OrderStatusService:
    """订单状态服务类
    
    提供订单状态一致性检查功能，根据订单项状态验证订单状态是否正确。
    
    订单状态检查规则：
    1. PENDING：所有订单项只允许为 PENDING
    2. CANCELLED：所有订单项只允许为 PENDING
    3. REFUNDED：所有订单项只允许为 REFUNDED
    4. PAID：必须包含 PAID 订单项，允许 VERIFIED 存在
    5. REFUNDED_PARTIAL：必须同时包含 PAID 和 REFUNDED 订单项，允许 VERIFIED 存在
    6. COMPLETED：必须包含 VERIFIED 订单项，允许 REFUNDED 存在
    """
    
    def __init__(self):
        self.valid_order_statuses = {
            OrderStatus.PENDING,
            OrderStatus.PAID,
            OrderStatus.REFUNDED_PARTIAL,
            OrderStatus.REFUNDED,
            OrderStatus.CANCELLED,
            OrderStatus.COMPLETED
        }
    
    def _validate_order_status(self, order_status: OrderStatus, item_statuses: List[OrderStatus]) -> bool:
        """
        根据订单项状态验证订单状态是否正确
        
        Args:
            order_status: 订单状态
            item_statuses: 订单项状态列表
            
        Returns:
            bool: 验证是否通过
        """
        if not item_statuses:
            return False
            
        item_status_set = set(item_statuses)
        
        if order_status == OrderStatus.PENDING:
            # 处理中：所有订单项只允许为 PENDING
            return item_status_set == {OrderStatus.PENDING}
        
        elif order_status == OrderStatus.CANCELLED:
            # 已取消：所有订单项只允许为 PENDING
            return item_status_set == {OrderStatus.PENDING}
        
        elif order_status == OrderStatus.REFUNDED:
            # 已退款：所有订单项只允许为 REFUNDED
            return item_status_set == {OrderStatus.REFUNDED}
        
        elif order_status == OrderStatus.PAID:
            # 已支付：必须包含 PAID，允许 VERIFIED 存在
            allowed_statuses = {OrderStatus.PAID, OrderStatus.VERIFIED}
            return (OrderStatus.PAID in item_status_set and 
                    item_status_set.issubset(allowed_statuses))
        
        elif order_status == OrderStatus.REFUNDED_PARTIAL:
            # 部分退款：必须同时包含 PAID 和 REFUNDED，允许 VERIFIED 存在
            allowed_statuses = {OrderStatus.PAID, OrderStatus.REFUNDED, OrderStatus.VERIFIED}
            return (OrderStatus.PAID in item_status_set and 
                    OrderStatus.REFUNDED in item_status_set and
                    item_status_set.issubset(allowed_statuses))
        
        elif order_status == OrderStatus.COMPLETED:
            # 已完成：必须包含 VERIFIED，允许 REFUNDED 存在
            allowed_statuses = {OrderStatus.VERIFIED, OrderStatus.REFUNDED}
            return (OrderStatus.VERIFIED in item_status_set and 
                    item_status_set.issubset(allowed_statuses))
        
        return False
    
    def check(self, order: Order) -> OrderStatusCheckResult:
        """
        检查单个订单状态是否正确
        
        Args:
            order: 订单对象
            
        Returns:
            OrderStatusCheckResult: 检查结果，包含：
                - is_valid: 是否检查通过
                - order_id: 订单ID
                - order_type: 订单类型
                - order_status: 订单状态
                - order_items: 订单项ID和状态列表
        """
        # 验证订单状态是否有效
        if order.status not in self.valid_order_statuses:
            return OrderStatusCheckResult(
                is_valid=False,
                order_id=order.id,
                order_type=order.type,
                order_status=order.status,
                order_items=[{
                    "order_item_id": item.id,
                    "status": item.status
                } for item in order.items]
            )
        
        # 获取订单项状态列表
        item_statuses = [item.status for item in order.items]
        
        # 验证订单状态与订单项状态的一致性
        is_valid = self._validate_order_status(order.status, item_statuses)
        
        return OrderStatusCheckResult(
            is_valid=is_valid,
            order_id=order.id,
            order_type=order.type,
            order_status=order.status,
            order_items=[{
                "order_item_id": item.id,
                "status": item.status
            } for item in order.items]
        )
    
    def check_all(self, session: Session) -> Tuple[bool, List[OrderStatusCheckResult]]:
        """
        检查所有订单状态是否正确
        
        Args:
            session: 数据库会话
            
        Returns:
            Tuple[bool, List[OrderStatusCheckResult]]: 包含：
                - bool: 是否全部检查通过
                - List[OrderStatusCheckResult]: 未通过检查的订单列表
        """
        failed_orders = []
        
        # 分批获取所有订单，避免内存占用过大
        batch_size = 100
        offset = 0
        
        while True:
            orders = order_dao.get_list(session, skip=offset, limit=batch_size)
            
            if not orders:
                break
            
            for order in orders:
                check_result = self.check(order)
                if not check_result.is_valid:
                    failed_orders.append(check_result)
            
            offset += batch_size
        
        all_passed = len(failed_orders) == 0
        
        return all_passed, failed_orders
    
    def calculate_status(self, order: Order) -> OrderStatus:
        """
        根据订单的所有订单项状态计算出订单的状态
        
        Args:
            order: 订单对象
            
        Returns:
            OrderStatus: 计算得出的订单状态
            
        状态计算规则：
        1. 处理中（PENDING）：所有订单项只允许为PENDING
        2. 已取消（CANCELLED）：所有订单项只允许为PENDING（且订单被手动取消）
        3. 已退款（REFUNDED）：所有订单项只允许为REFUNDED
        4. 已支付（PAID）：订单项中必须包含PAID，同时允许VERIFIED存在（非必须）
        5. 部分退款（REFUNDED_PARTIAL）：订单项中必须同时包含PAID和REFUNDED，同时允许VERIFIED存在（非必须）
        6. 已完成（COMPLETED）：订单项中必须包含VERIFIED，同时允许REFUNDED存在（非必须）
        """
        if not order.items:
            return order.status
            
        # 获取所有订单项的状态集合
        item_status_set = set(item.status for item in order.items)
        
        # 规则3：已退款 - 所有订单项只允许为REFUNDED
        if item_status_set == {OrderStatus.REFUNDED}:
            return OrderStatus.REFUNDED
            
        # 规则6：已完成 - 必须包含VERIFIED，同时允许REFUNDED存在
        if OrderStatus.VERIFIED in item_status_set:
            allowed_completed_statuses = {OrderStatus.VERIFIED, OrderStatus.REFUNDED}
            if item_status_set.issubset(allowed_completed_statuses):
                return OrderStatus.COMPLETED
                
        # 规则5：部分退款 - 必须同时包含PAID和REFUNDED，同时允许VERIFIED存在
        if (OrderStatus.PAID in item_status_set and 
            OrderStatus.REFUNDED in item_status_set):
            allowed_partial_refund_statuses = {OrderStatus.PAID, OrderStatus.REFUNDED, OrderStatus.VERIFIED}
            if item_status_set.issubset(allowed_partial_refund_statuses):
                return OrderStatus.REFUNDED_PARTIAL
                
        # 规则4：已支付 - 必须包含PAID，同时允许VERIFIED存在
        if OrderStatus.PAID in item_status_set:
            allowed_paid_statuses = {OrderStatus.PAID, OrderStatus.VERIFIED}
            if item_status_set.issubset(allowed_paid_statuses):
                return OrderStatus.PAID
                
        # 规则1：处理中 - 所有订单项只允许为PENDING
        if item_status_set == {OrderStatus.PENDING}:
            # 注意：这里不直接返回PENDING，因为可能是CANCELLED状态
            # 需要根据其他业务逻辑判断是PENDING还是CANCELLED
            # 如果订单已被标记为取消，则返回CANCELLED，否则返回PENDING
            if hasattr(order, 'is_cancelled') and order.is_cancelled:
                return OrderStatus.CANCELLED
            return OrderStatus.PENDING
            
        # 默认情况：如果无法匹配任何规则，返回PENDING
        return order.status


# 创建服务实例
order_status_service = OrderStatusService()
