from datetime import datetime
from typing import Optional, <PERSON>, Tuple, Any

from pydantic import BaseModel, ConfigDict, field_serializer

from app.models.order import OrderStatus, PaymentStatus, PaymentMethod, OrderType, ReservationStatus


# OrderItem schemas
class OrderItemBase(BaseModel):
    product_id: int
    quantity: int
    price: float
    subtotal: float
    final_price: float
    payable_amount: float
    discount_amount: float = 0.0
    pricing_remark: Optional[str] = None


class OrderItemCreate(OrderItemBase):
    order_id: int


class OrderItemUpdate(BaseModel):
    product_id: Optional[int] = None
    quantity: Optional[int] = None
    price: Optional[float] = None
    subtotal: Optional[float] = None
    final_price: Optional[float] = None
    payable_amount: Optional[float] = None
    discount_amount: Optional[float] = None


class OrderItemResponse(OrderItemCreate):
    id: int
    order_id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at")
    def serialize_datetime(self, value: datetime) -> str:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class OrderItemInDB(OrderItemResponse):
    model_config = ConfigDict(from_attributes=True)


class OrderItem(OrderItemInDB):
    pass


# Order schemas
class OrderBase(BaseModel):
    status: OrderStatus = OrderStatus.PENDING
    payment_status: PaymentStatus = PaymentStatus.UNPAID
    payment_time: Optional[datetime] = None
    total_amount: float
    payable_amount: float
    actual_amount_paid: float
    discount_amount: float = 0.0
    payment_method: PaymentMethod = PaymentMethod.ACCOUNT_BALANCE
    type: OrderType = OrderType.ORDER
    pricing_remark: Optional[str] = None


class OrderCreate(OrderBase):
    user_id: int
    order_no: Optional[str] = None  # 改为可选，让DAO自动生成
    items: Optional[List[OrderItemBase]] = []


class RechargeOrderCreate(OrderCreate):
    """充值订单创建Schema"""
    type: OrderType = OrderType.RECHARGE


class DirectSaleOrderCreate(OrderCreate):
    """直销订单创建Schema"""
    type: OrderType = OrderType.DIRECT_SALE


class ReservationOrderCreate(OrderCreate):
    """预订订单创建Schema"""
    type: OrderType = OrderType.RESERVATION
    reservation_status: ReservationStatus = ReservationStatus.PENDING


class OrderUpdate(BaseModel):
    status: Optional[OrderStatus] = None
    payment_status: Optional[PaymentStatus] = None
    payment_time: Optional[datetime] = None
    total_amount: Optional[float] = None
    payable_amount: Optional[float] = None
    actual_amount_paid: Optional[float] = None
    discount_amount: Optional[float] = None
    payment_method: Optional[PaymentMethod] = None


class RechargeOrderUpdate(OrderUpdate):
    """充值订单更新Schema"""
    pass  # 充值订单目前没有额外字段，如需要可在此添加


class DirectSaleOrderUpdate(OrderUpdate):
    """直销订单更新Schema"""
    pass  # 直销订单目前没有额外字段，如需要可在此添加


class ReservationOrderUpdate(OrderUpdate):
    """预订订单更新Schema"""
    reservation_status: Optional[ReservationStatus] = None


class OrderResponse(OrderCreate):
    type: OrderType = OrderType.ORDER
    id: int
    created_at: datetime
    updated_at: datetime

    @field_serializer("created_at", "updated_at", "payment_time")
    def serialize_datetime(self, value: datetime) -> str:
        if value is None:
            return None
        return value.strftime("%Y-%m-%d %H:%M:%S")


class OrderInDB(OrderResponse):
    model_config = ConfigDict(from_attributes=True)


class Order(OrderInDB):
    pass


# 关联关系Schema
class OrderWithItems(Order):
    items: List[OrderItem] = []


# 优惠券优惠详情
class CouponDiscountDetail(BaseModel):
    """优惠券优惠详情"""
    coupon_usage_record_id: int
    coupon_id: int
    coupon_name: str
    quantity: int
    discount_amount: float


# 详细响应模型
class OrderDetailResponse(OrderResponse):
    """包含订单项信息的订单详细响应"""
    items: List[OrderItemResponse] = []
    total_discount_amount: Optional[float] = 0.0  # 总优惠金额
    coupon_discounts: Optional[List[CouponDiscountDetail]] = []  # 优惠券优惠详情


# 支付状态更新模型
class OrderPaymentStatusUpdate(BaseModel):
    payment_status: PaymentStatus
    payment_time: Optional[datetime] = None


# 订单状态更新模型
class OrderStatusUpdate(BaseModel):
    status: OrderStatus


# 预订订单状态更新模型
class ReservationStatusUpdate(BaseModel):
    reservation_status: ReservationStatus


class ReservationRequest(BaseModel):
    """预订请求"""
    reservation_period: str
    reservation_time: datetime
    rule_id: int


class OrderRequestItem(BaseModel):
    """订购产品项"""
    product_id: int
    quantity: int
    reservation_requests: List[ReservationRequest] = []


class OrderRequest(BaseModel):
    """订购产品请求模型"""
    user_id: int
    products: List[OrderRequestItem]
    payment_method: PaymentMethod = PaymentMethod.ACCOUNT_BALANCE


class CreateOrderResponse(BaseModel):
    """创建订单响应"""
    success: bool
    message: str
    order: Optional[OrderDetailResponse] = None


class PreOrderResult(BaseModel):
    """预订单结果"""
    order_items: List[OrderItemBase]
    total_amount: float
    total_payable_amount: float
    temp_reservation_requests: List[Tuple[int, int, List[Any]]]  # [(index, product_id, reservation_requests)]
    order_payable_amount: float
    pricing_remark: Optional[str] = None
