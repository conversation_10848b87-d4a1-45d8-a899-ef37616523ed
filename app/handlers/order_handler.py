from app.events import OrderEvent
from app.events.handlers import event_handler, logger
from app.events.models import OrderEventAction
from app.tasks.notifications.biz_dinner import send_biz_reservation_notification
from app.core.deps import get_db
from app.service.coupon_distribute import CouponDistributeService
from app.models.order import OrderType
from app.dao.order import order_dao


@event_handler(OrderEvent)
async def handle_order_events(event: OrderEvent):
    """Handle order-related events."""
    # 打印 OrderEvent 的所有信息
    logger.debug("=" * 60)
    logger.debug("  OrderEvent 详细信息")
    logger.debug("=" * 60)
    logger.debug(f"  事件ID: {event.id}")
    logger.debug(f"  时间戳: {event.timestamp}")
    logger.debug(f"  来源: {event.source}")
    logger.debug(f"️  事件类型: {event.event_type}")
    logger.debug(f"  操作: {event.action}")
    logger.debug(f"  订单ID: {event.order_id}")
    logger.debug(f"  订单号: {event.order_no}")
    logger.debug(f"  用户ID: {event.user_id}")
    logger.debug(f"  金额: {event.amount}")
    logger.debug(f"  状态: {event.status}")
    logger.debug(f"  支付状态: {event.payment_status}")
    logger.debug(f"  附加数据: {event.additional_data}")
    logger.debug("-" * 60)
    logger.debug(f"  完整事件数据 (JSON): {event.dict()}")
    logger.debug("=" * 60)
    print("==============================================事件已接收==============================================")
    print(event)
    # 根据不同的操作类型进行处理
    if event.action == OrderEventAction.CREATED:
        pass

    elif event.action == OrderEventAction.PAID:
        logger.info(f"处理订单支付: {event.order_no}, amount: {event.amount}")
        await send_biz_reservation_notification(event.order_id, event.action)

        try:
            session = next(get_db())
            order = order_dao.get(session, event.order_id)
            if order.type.value == OrderType.DIRECT_SALE.value:
                distributed_coupons = CouponDistributeService.distribute_coupons_by_order(session, order)
                if distributed_coupons:
                    logger.info(f"订单支付事件，直销订单触发优惠券发放成功，共发放 {len(distributed_coupons.get("distributed_coupons", []))} 张优惠券")
                else:
                    logger.info("订单支付事件，直销订单未触发优惠券发放")
            else:
                logger.info("订单支付事件，非直销订单，不发放优惠券")
        except Exception as e:
            logger.error(f"处理订单支付事件时发放优惠券失败: {str(e)}")
        finally:
            if 'session' in locals():
                session.close()

    elif event.action == OrderEventAction.CANCELLED:
        logger.info(f"处理订单取消: {event.order_no}")
        await send_biz_reservation_notification(event.order_id, event.action)

    elif event.action == OrderEventAction.UPDATED:
        pass

    elif event.action == OrderEventAction.REFUNDED:
        pass

    elif event.action == OrderEventAction.COMPLETED:
        pass
    else:
        pass

    logger.info(f"  OrderEvent 处理完成: {event.id}")
    logger.info("=" * 60)
