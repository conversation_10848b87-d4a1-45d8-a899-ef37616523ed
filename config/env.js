// 环境配置文件
// config/env.js

const ENV_CONFIG = {
  // 开发环境 (开发者工具)
  development: {
    name: '开发环境',
    baseUrlHost: 'https://vegan.yiheship.com',
    baseUrl: 'http://127.0.0.1:8000/api/v1/wx',
    debug: true,
    defaultStoreId: 1  // 默认商店ID
  },
  // 体验版
  trial: {
    name: '体验版',
    baseUrlHost: 'https://vegan.yiheship.com',
    baseUrl: 'https://uat-vegan.yiheship.com/api/v1/wx',
    debug: true,
    defaultStoreId: 1  // 默认商店ID
  },
  // 线上版本
  release: {
    name: '线上版本',
    baseUrlHost: 'https://vegan.yiheship.com',
    baseUrl: 'https://vegan.yiheship.com/api/v1/wx',
    debug: false,
    defaultStoreId: 1  // 默认商店ID
  }
}

/**
 * 获取当前环境配置
 * @returns {Object} 当前环境的配置对象
 */
function getCurrentEnvConfig() {
  try {
    const accountInfo = wx.getAccountInfoSync()
    const envVersion = accountInfo.miniProgram.envVersion
    
    console.log('当前小程序版本:', envVersion)
    
    // 根据版本返回对应配置
    let config
    switch (envVersion) {
      case 'develop':
        config = ENV_CONFIG.development
        break
      case 'trial':
        config = ENV_CONFIG.trial
        break
      case 'release':
        config = ENV_CONFIG.release
        break
      default:
        // 默认使用开发环境配置
        config = ENV_CONFIG.development
        console.warn('未知的环境版本，使用开发环境配置:', envVersion)
    }
    
    console.log(`当前环境: ${config.name}`)
    console.log(`API地址: ${config.baseUrl}`)
    
    return config
  } catch (error) {
    console.error('获取环境配置失败，使用开发环境配置:', error)
    return ENV_CONFIG.development
  }
}

/**
 * 获取指定环境的配置
 * @param {string} env 环境名称 (development/trial/release)
 * @returns {Object} 指定环境的配置对象
 */
function getEnvConfig(env) {
  return ENV_CONFIG[env] || ENV_CONFIG.development
}

/**
 * 获取所有环境配置
 * @returns {Object} 所有环境配置
 */
function getAllEnvConfig() {
  return ENV_CONFIG
}

module.exports = {
  getCurrentEnvConfig,
  getEnvConfig,
  getAllEnvConfig,
  ENV_CONFIG
}
