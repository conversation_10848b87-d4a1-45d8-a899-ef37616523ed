#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
优惠券使用记录回退脚本
通过输入 coupon_usage_record_id，回退该记录的使用状态
"""

import sys
import os
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session, selectinload

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.coupon import CouponUsageRecord, CouponUsageStatus
from app.dao.coupon import coupon_usage_record_dao
from app.service.order_cancel_service import OrderCancelService


def print_separator(title: str = "", width: int = 80):
    """打印分隔线"""
    if title:
        print(f"\n{'=' * width}")
        print(f"{title:^{width}}")
        print('=' * width)
    else:
        print('-' * width)


def print_coupon_record_info(record: CouponUsageRecord):
    """打印优惠券使用记录信息"""
    if not record:
        print("❌ 记录不存在")
        return
        
    print("\n📋 优惠券使用记录信息:")
    print_separator("", 60)
    print(f"  {'记录ID':<20}: {record.id}")
    print(f"  {'优惠券ID':<20}: {record.coupon_id}")
    print(f"  {'优惠券批次ID':<20}: {record.coupon_batch_id}")
    print(f"  {'用户ID':<20}: {record.user_id}")
    print(f"  {'订单ID':<20}: {record.order_id or 'None'}")
    print(f"  {'订单项ID':<20}: {record.order_item_id or 'None'}")
    print(f"  {'优惠金额':<20}: {record.discount_amount}")
    print(f"  {'使用状态':<20}: {record.status.value}")
    print(f"  {'分发渠道':<20}: {record.distribution_channel.value if record.distribution_channel else 'None'}")
    print(f"  {'使用时间':<20}: {record.used_at.strftime('%Y-%m-%d %H:%M:%S') if record.used_at else 'None'}")
    print(f"  {'创建时间':<20}: {record.created_at.strftime('%Y-%m-%d %H:%M:%S') if record.created_at else 'None'}")
    print(f"  {'更新时间':<20}: {record.updated_at.strftime('%Y-%m-%d %H:%M:%S') if record.updated_at else 'None'}")
    
    # 显示关联信息
    if hasattr(record, 'coupon') and record.coupon:
        print(f"  {'关联优惠券名称':<20}: {record.coupon.name}")
    if hasattr(record, 'user') and record.user:
        print(f"  {'关联用户名':<20}: {record.user.username}")


def rollback_coupon_usage_record(record_id: int):
    """回退优惠券使用记录"""
    db: Session = SessionLocal()
    
    try:
        print_separator(f"优惠券使用记录回退 - Record ID: {record_id}")
        
        # 查询优惠券使用记录
        record = db.query(CouponUsageRecord).options(
            selectinload(CouponUsageRecord.coupon),
            selectinload(CouponUsageRecord.user),
            selectinload(CouponUsageRecord.order),
            selectinload(CouponUsageRecord.order_item)
        ).filter(CouponUsageRecord.id == record_id).first()
        
        if not record:
            print(f"\n❌ 未找到 ID 为 {record_id} 的优惠券使用记录")
            return False
            
        # 显示当前记录信息
        print_coupon_record_info(record)
        
        # 检查当前状态
        if record.status != CouponUsageStatus.USED:
            print(f"\n⚠️  警告: 当前记录状态为 '{record.status.value}'，只有状态为 'used' 的记录才能回退")
            
            # 询问是否继续
            if not get_user_confirmation("是否仍要继续回退操作？"):
                print("❌ 用户取消操作")
                return False
        
        # 显示将要进行的操作
        print("\n🔄 即将执行的回退操作:")
        print("  • 状态: used → valid")
        print("  • 订单ID: 清空")
        print("  • 订单项ID: 清空") 
        print("  • 优惠金额: 重置为 0.0")
        print("  • 使用时间: 清空")
        
        # 确认操作
        if not get_user_confirmation("确认执行回退操作？"):
            print("❌ 用户取消操作")
            return False
        
        # 记录原始值
        original_status = record.status
        original_order_id = record.order_id
        original_order_item_id = record.order_item_id
        original_discount_amount = record.discount_amount
        original_used_at = record.used_at
        
        try:
            # 执行回退操作
            print("\n🔄 正在执行回退操作...")
            
            # 更新记录状态
            update_data = {
                "status": CouponUsageStatus.VALID,
                "order_id": None,
                "order_item_id": None,
                "discount_amount": 0.0,
                "used_at": None
            }
            
            coupon_usage_record_dao.update(db, record_id, **update_data)
            
            # 提交事务
            db.commit()
            
            print("✅ 回退操作成功完成！")
            
            # 显示操作结果
            print("\n📊 回退操作详情:")
            print_separator("", 60)
            print(f"  {'字段':<15} {'原值':<20} {'新值':<20}")
            print_separator("", 60)
            print(f"  {'状态':<15} {original_status.value:<20} {'valid':<20}")
            print(f"  {'订单ID':<15} {str(original_order_id or 'None'):<20} {'None':<20}")
            print(f"  {'订单项ID':<15} {str(original_order_item_id or 'None'):<20} {'None':<20}")
            print(f"  {'优惠金额':<15} {original_discount_amount:<20} {'0.0':<20}")
            print(f"  {'使用时间':<15} {str(original_used_at or 'None'):<20} {'None':<20}")
            
            return True
            
        except Exception as e:
            print(f"❌ 回退操作失败: {str(e)}")
            db.rollback()
            return False
            
    except Exception as e:
        print(f"\n❌ 查询过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
        
    finally:
        db.close()


def get_user_confirmation(message: str) -> bool:
    """获取用户确认"""
    while True:
        try:
            response = input(f"\n{message} (y/n): ").strip().lower()
            if response in ['y', 'yes', '是']:
                return True
            elif response in ['n', 'no', '否']:
                return False
            else:
                print("请输入 y/yes/是 或 n/no/否")
        except KeyboardInterrupt:
            print("\n\n❌ 用户中断操作")
            return False


def batch_rollback_by_order(order_id: int):
    """批量回退指定订单的所有优惠券使用记录"""
    db: Session = SessionLocal()
    
    try:
        print_separator(f"批量回退订单优惠券 - Order ID: {order_id}")
        
        # 使用已有的回退方法
        result = OrderCancelService.rollback_coupon_usage(db, order_id=order_id)
        
        if result["success"]:
            print(f"✅ {result['message']}")
            return True
        else:
            print(f"❌ {result['message']}")
            return False
            
    except Exception as e:
        print(f"❌ 批量回退失败: {str(e)}")
        return False
    finally:
        db.close()


def main():
    """主函数"""
    print("🎫 优惠券使用记录回退工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 从命令行参数获取记录ID
        try:
            if sys.argv[1] == "--order":
                # 批量回退订单的优惠券
                if len(sys.argv) < 3:
                    print("❌ 请提供订单ID")
                    sys.exit(1)
                order_id = int(sys.argv[2])
                success = batch_rollback_by_order(order_id)
                sys.exit(0 if success else 1)
            else:
                # 单个记录回退
                record_id = int(sys.argv[1])
                success = rollback_coupon_usage_record(record_id)
                sys.exit(0 if success else 1)
        except ValueError:
            print("❌ 请输入有效的记录ID（数字）")
            sys.exit(1)
    else:
        # 交互式输入
        while True:
            try:
                print("\n选择操作模式:")
                print("1. 单个记录回退")
                print("2. 按订单批量回退")
                print("q. 退出")
                
                mode = input("\n请选择操作模式 (1/2/q): ").strip()
                
                if mode.lower() == 'q':
                    print("👋 再见！")
                    break
                elif mode == '1':
                    record_id_input = input("\n请输入优惠券使用记录ID: ").strip()
                    try:
                        record_id = int(record_id_input)
                        rollback_coupon_usage_record(record_id)
                    except ValueError:
                        print("❌ 请输入有效的记录ID（数字）")
                elif mode == '2':
                    order_id_input = input("\n请输入订单ID: ").strip()
                    try:
                        order_id = int(order_id_input)
                        batch_rollback_by_order(order_id)
                    except ValueError:
                        print("❌ 请输入有效的订单ID（数字）")
                else:
                    print("❌ 请输入有效的选项")
                    
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()
