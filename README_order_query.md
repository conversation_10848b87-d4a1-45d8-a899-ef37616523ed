# 订单信息查询脚本使用说明

## 功能介绍

`order_info_query.py` 是一个用于查询订单详细信息的脚本，可以通过输入订单ID来查看：

- 📋 **订单基本信息**：包括父类 `Order` 和子类（如 `ReservationOrder`、`RechargeOrder` 等）的所有属性
- 📦 **订单项信息**：所有 `OrderItem` 的详细属性
- 📅 **预约请求信息**：关联的 `ReservationRequest` 信息
- 🎫 **优惠券使用记录**：相关的 `CouponUsageRecord` 信息

## 主要特性

✅ **完整的继承信息**：显示订单对象的所有属性，包括从父类继承的属性  
✅ **字段来源标识**：标明字段来自哪个表，便于理解数据结构  
✅ **美观的输出格式**：清晰的分隔线和层次结构  
✅ **多种使用方式**：支持命令行参数和交互式输入  
✅ **错误处理**：完善的异常处理和用户友好的错误提示  

## 使用方法

### 方法1：命令行参数

```bash
# 查询订单ID为123的信息
python order_info_query.py 123
```

### 方法2：交互式输入

```bash
# 启动交互模式
python order_info_query.py

# 然后根据提示输入订单ID
请输入订单ID（输入 'q' 退出）: 123
```

### 方法3：使用虚拟环境

```bash
# 如果使用poetry
poetry run python order_info_query.py 123

# 或者激活虚拟环境后运行
source venv/bin/activate
python order_info_query.py 123
```

## 输出示例

```
🔍 订单信息查询工具
==================================================

================================================================================
                          订单信息查询 - Order ID: 123
================================================================================

📋 订单基本信息:
============================================================
  对象类型                    : ReservationOrder
  表名                       : reservation_orders

============================================================
  actual_amount_paid         : 100.0
  created_at                 : 2023-12-01 10:30:00
  discount_amount            : 0.0
  id                         : 123
  order_no                   : ORD20231201001
  payment_method             : wechat_pay
  payment_status             : paid
  status                     : paid
  total_amount               : 100.0
  type                       : reservation
  updated_at                 : 2023-12-01 10:35:00
  user_id                    : 456
  reservation_status (reservation_orders): approved

📦 订单项信息 (共 2 条):

  第 1 条记录 (OrderItem):
--------------------------------------------------
    created_at                       : 2023-12-01 10:30:00
    discount_amount                  : 0.0
    final_price                      : 50.0
    id                               : 789
    order_id                         : 123
    payable_amount                   : 50.0
    price                            : 50.0
    product_id                       : 101
    quantity                         : 1
    status                           : paid
    subtotal                         : 50.0
    updated_at                       : 2023-12-01 10:35:00

  第 2 条记录 (OrderItem):
--------------------------------------------------
    ...

📅 预约请求信息 (共 1 条):
...

🎫 优惠券使用记录 (共 0 条):
无数据

--------------------------------------------------------------------------------
                                  查询完成
--------------------------------------------------------------------------------
```

## 注意事项

1. **数据库连接**：脚本会自动使用项目配置的数据库连接
2. **权限要求**：需要有读取数据库的权限
3. **环境变量**：确保正确设置了数据库连接相关的环境变量
4. **依赖安装**：确保安装了所有必要的Python依赖包

## 故障排除

### 常见问题

**Q: 提示 "未找到订单"**  
A: 检查输入的订单ID是否正确，或者该订单是否真实存在

**Q: 数据库连接失败**  
A: 检查数据库服务是否运行，以及环境变量配置是否正确

**Q: 模块导入错误**  
A: 确保在项目根目录下运行脚本，并且安装了所有依赖

### 调试模式

如果遇到问题，脚本会自动打印详细的错误信息和堆栈跟踪，帮助定位问题。

## 扩展功能

脚本支持轻松扩展，可以根据需要添加：
- 更多关联表的查询
- 自定义输出格式
- 导出功能（如导出为JSON或CSV）
- 批量查询功能
