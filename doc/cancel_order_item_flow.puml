@startuml cancel_order_item_flow
!define RECTANGLE class
!theme plain

title cancel_order_item 方法流程图

start

:接收请求参数;
note right
  - task_info: 包含order_item_id, order_no, order_id
  - token: 用户身份令牌
  - db: 数据库会话
  - event_bus: 事件总线
end note

:验证用户身份;
note right: WeChatUserService.verify_token(db, token)

if (用户身份验证成功?) then (否)
  :返回401未登录错误;
  stop
endif

:提取请求参数;
note right
  - order_item_id: 订单项ID
  - order_no: 订单号
  - order_id: 订单ID
end note

if (必要参数完整?) then (否)
  :返回400参数错误;
  stop
endif

partition "数据验证阶段" {
  :获取订单项信息;
  note right: order_item_dao.get(db, order_item_id)
  
  if (订单项存在?) then (否)
    :返回404订单项不存在;
    stop
  endif
  
  :获取主订单信息;
  note right: order_dao.get(db, order_item.order_id)
  
  if (订单存在?) then (否)
    :返回404订单不存在;
    stop
  endif
  
  :判断订单类型;
  if (是商务餐订单?) then (是)
    :is_business_dining = True;
    :退款金额 = 整个订单实付金额;
  else (否)
    :is_business_dining = False;
    :退款金额 = 当前订单项金额;
  endif
}

partition "权限和状态验证" {
  if (订单状态 == PAID?) then (否)
    :返回400请勿重复申请退款;
    stop
  endif
  
  if (订单用户ID == 当前用户ID?) then (否)
    :返回403无权操作此订单;
    stop
  endif
  
  :获取预约请求信息;
  note right: reservation_request_dao.get_by_order_item_id_and_user_id()
  
  if (预约信息存在?) then (否)
    :返回404预约信息不存在;
    stop
  endif
  
  :获取预约规则项;
  if (规则项存在?) then (否)
    :返回404预约规则项不存在;
    stop
  endif
}

partition "取消时间验证" {
  :解析预约开始时间;
  :获取当前时间;
  
  if (规则项有取消截止时间?) then (是)
    :计算取消截止时间点;
    if (当前时间 > 取消截止时间?) then (是)
      :返回500已超过取消截止时间;
      stop
    endif
  else (否)
    :使用默认截止时间(就餐前一天22点);
    if (当前时间 > 默认截止时间?) then (是)
      :返回500已超过默认取消截止时间;
      stop
    endif
  endif
}

partition "退款金额验证" {
  if (订单实付金额 >= 退款金额?) then (否)
    :返回500退款失败，总订单实付金额不足;
    stop
  endif
}

partition "事务处理开始" {
  :开始数据库事务;
  
  :更新预约状态为已取消;
  note right: reservation_request_dao.cancel_reservation()
  
  :生成退款单号;
  note right: out_refund_no = f"refund_{order_no}_{order_item_id}"
  
  :重新获取订单对象;
  note right: 确保session状态正确
  
  :检查支付方式类型;
  if (是混合支付?) then (是)
    :处理混合支付退款;
    note right
      调用 handle_split_payment_refund()
      - 按比例分配退款金额
      - 分别处理企业和个人部分退款
    end note
    
    partition "混合支付退款处理" {
      :计算各部分退款比例;
      note right
        enterprise_refund = (企业支付/总支付) * 退款金额
        personal_refund = 退款金额 - enterprise_refund
      end note
      
      if (企业退款金额 > 0?) then (是)
        :处理企业账户退款;
        note right: refund_enterprise_payment()
      endif
      
      if (个人退款金额 > 0?) then (是)
        :处理个人退款;
        note right: refund_personal_payment()
        
        if (个人部分是微信支付?) then (是)
          :调用微信退款接口;
        else (否)
          :退款到个人账户余额;
        endif
      endif
    }
    
  elseif (支付方式 == 微信支付?) then (是)
    :处理微信支付退款;
    
    if (是商务餐订单?) then (是)
      :获取所有支付记录;
      :按比例分配退款到各支付记录;
      
      partition "商务餐微信退款" {
        :遍历所有支付记录;
        :计算每个支付记录的退款金额;
        :为每个支付记录创建退款请求;
        note right: wechat_service.create_refund()
      }
      
    else (否)
      :获取支付记录;
      :创建微信退款请求;
      note right: wechat_service.create_refund()
    endif
    
  elseif (支付方式 == 个人账户余额?) then (是)
    :处理个人账户余额退款;
    :增加用户账户余额;
    :创建退款交易记录;
    
  elseif (支付方式 == 企业账户余额?) then (是)
    :处理企业账户余额退款;
    :获取企业账户交易记录;
    :增加企业账户余额;
    :创建退款交易记录;
  endif
}

partition "订单状态更新" {
  if (是商务餐订单?) then (是)
    :取消所有订单项;
    :更新订单状态为已退款;
    note right
      order.status = OrderStatus.REFUNDED
      order.payment_status = PaymentStatus.REFUNDED
    end note
  else (否)
    :只取消当前订单项;
    :order_item.status = OrderStatus.REFUNDED;
  endif
  
  if (支付方式 != 微信支付?) then (是)
    :更新订单实付金额;
    :检查所有订单项状态;
    if (所有订单项都已退款?) then (是)
      :订单状态 = 已退款;
    else (否)
      :订单状态 = 部分退款;
    endif
  endif
}

:提交数据库事务;

if (事务提交成功?) then (否)
  :回滚事务;
  :返回500取消失败;
  stop
endif

:发布订单取消事件;
note right
  OrderEvent(
    action=OrderEventAction.CANCELLED,
    order_id, order_no, user_id,
    additional_data: {
      is_business_dining,
      order_item_id
    }
  )
end note

:返回200取消成功;

stop

note bottom
  异常处理：
  - 每个阶段都有相应的异常处理
  - 事务失败时自动回滚
  - 详细的日志记录便于问题排查
end note

@enduml

