@startuml get_reservation_info_flow
!define RECTANGLE class
!theme plain

title get_reservation_info 方法流程图

start

:接收参数;
note right
  - db: 数据库会话
  - type: 预约类型 (employee/personal/personal_business/company_business)
  - user_id: 用户ID
  - source: 请求来源 (topic/admin)
  - meal_type: 餐食类型
  - qr_type: 二维码类型
  - filter_existing_reservations: 是否过滤现有预订
end note

:记录请求日志;

:判断餐食类型;
if (type包含'business'?) then (是)
  :meal_type = MealType.BUSINESS;
else (否)
  :meal_type = MealType.BUFFET;
endif

:根据餐食类型获取产品列表;
note right: product_dao.get_products_by_meal_type(db, meal_type)

:收集所有产品的预订规则;
note right
  遍历product_list，
  收集每个product.rules
end note

:将规则转换为字典格式;
note right
  使用 rule_dao.to_dict(rule)
  转换为前端可用格式
end note

:获取可用时间段列表;
partition "处理每个规则" {
  :遍历rule_dict_list;
  :调用wx_miniapp_get_availability_list获取可用时间段;
  note right
    service_reservation_service.wx_miniapp_get_availability_list(
      db, rule_id, source, meal_type, qr_type
    )
  end note
  :按日期分组时间段;
}

:构建日期列表;
partition "处理日期信息" {
  :遍历task_list;
  :格式化日期显示;
  note right
    - 转换为 MM-DD 周X 格式
    - 按start_time排序时间段
    - 初始化各种状态字段
  end note
  :按日期排序;
}

:初始化企业权限检查变量;

if (type == 'employee'?) then (是)
  :获取用户关联的企业信息;
  note right: enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
  
  :检查企业晚餐权限;
  note right
    检查用户企业是否在
    settings.OPEN_ENTERPRISE_LIST 中
  end note
  
  if (没有晚餐权限?) then (是)
    :过滤晚餐时间段;
    partition "过滤晚餐时间段" {
      :遍历date_list;
      :检查每个时间段的餐食类型;
      if (是晚餐时间段?) then (是)
        :移除该时间段;
        :标记meal_type_status["dinner"] = "no_permission";
      else (否)
        :保留时间段;
      endif
    }
  endif
endif

if (type == 'employee' AND filter_existing_reservations?) then (是)
  :过滤已存在的预订;
  partition "过滤现有预订" {
    :遍历date_list;
    :检查当天是否已有企业账户支付的订单;
    note right
      order_dao.check_enterprise_order_in_day_by_meal_type(
        db, user_id, request_date_list, meal_type
      )
    end note
    
    if (已有该餐食类型的预订?) then (是)
      :移除对应时间段;
      :标记meal_type_status[meal_type] = "has_reservation";
    else (否)
      :保留时间段;
    endif
    
    if (所有时间段都被移除?) then (是)
      :设置整体不可选择原因;
      :添加到already_date_list;
    endif
  }
endif

:获取企业配置信息;
if (type == 'personal'?) then (是)
  :个人自助餐，enterprise_list为空;
elseif (type == 'employee'?) then (是)
  :获取用户关联的企业信息;
  note right: 添加到enterprise_list
elseif (type == 'personal_business'?) then (是)
  :个人商务餐，enterprise_list为空;
elseif (type == 'company_business'?) then (是)
  :获取用户管理的企业信息;
  note right
    检查用户是否为企业管理员
    (source != 'admin'时)
  end note
endif

:构建返回结果;
note right
  {
    "code": 200,
    "message": "success", 
    "data": {
      "has_dinner_permission": has_dinner_permission,
      "dateList": date_list,
      "reservationFee": 0,
      "enterprise_list": enterprise_list,
      "already_date_list": already_date_list
    }
  }
end note

:返回结果;

stop

@enduml

