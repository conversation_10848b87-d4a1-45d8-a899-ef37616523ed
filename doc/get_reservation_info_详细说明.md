# get_reservation_info 方法详细说明

## 概述

`get_reservation_info` 是 `ReservationService` 类中的核心方法，用于获取预约配置信息。该方法根据不同的预约类型、用户权限和业务规则，返回用户可预约的时间段列表和相关配置信息。

## 方法签名

```python
@staticmethod
def get_reservation_info(
    db: Session, 
    type: str, 
    user_id: int, 
    source: Optional[str] = None, 
    meal_type: Optional[str] = None, 
    qr_type: Optional[str] = None, 
    filter_existing_reservations: bool = True
) -> Dict[str, Any]
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `db` | `Session` | 是 | 数据库会话对象 |
| `type` | `str` | 是 | 预约类型，可选值：`employee`（员工餐）、`personal`（个人自助餐）、`personal_business`（个人商务餐）、`company_business`（企业商务餐） |
| `user_id` | `int` | 是 | 用户ID |
| `source` | `str` | 否 | 请求来源，可选值：`topic`、`admin` |
| `meal_type` | `str` | 否 | 餐食类型（当前版本中该参数会被重新赋值） |
| `qr_type` | `str` | 否 | 二维码类型 |
| `filter_existing_reservations` | `bool` | 否 | 是否过滤现有预订记录，默认为 `True` |

## 详细流程分析

### 第一阶段：初始化和参数处理

#### 1.1 日志记录
```python
logger.info(f"source: {source}")
logger.info(f"处理预约配置请求，类型: {type}，来源: {source}, 二维码类型: {qr_type}")
```
**作用**：记录请求的基本信息，便于调试和监控。

#### 1.2 餐食类型判断
```python
if 'business' in type:
    meal_type = MealType.BUSINESS
else:
    meal_type = MealType.BUFFET
```
**作用**：根据预约类型确定餐食类型。如果预约类型包含 "business"，则设置为商务餐，否则设置为自助餐。

### 第二阶段：获取产品和规则信息

#### 2.1 获取产品列表
```python
product_list = product_dao.get_products_by_meal_type(db, meal_type)
```
**作用**：根据餐食类型从数据库获取相应的产品列表。只获取状态为 `ACTIVE` 的产品。

#### 2.2 收集预订规则
```python
rule_list = []
for product in product_list:
    rule_list.extend(product.rules)
```
**作用**：遍历所有产品，收集每个产品关联的预订规则。这些规则定义了预约的时间段、容量限制等。

#### 2.3 规则格式转换
```python
rule_dict_list = []
for rule in rule_list:
    rule_dict = rule_dao.to_dict(rule)
    rule_dict_list.append(rule_dict)
```
**作用**：将规则对象转换为字典格式，便于后续处理和前端使用。

### 第三阶段：获取可用时间段

#### 3.1 获取时间段列表
```python
task_list = {}
for rule_dict in rule_dict_list:
    try:
        availability_list = service_reservation_service.wx_miniapp_get_availability_list(
            db, rule_dict["id"], source, meal_type, qr_type
        )
        for availability in availability_list:
            task_list.setdefault(availability["date"], []).append(availability)
    except Exception as e:
        pass
```
**作用**：
- 调用 `wx_miniapp_get_availability_list` 方法获取每个规则的可用时间段
- 按日期分组时间段信息
- 异常处理确保单个规则失败不影响整体流程

### 第四阶段：构建日期列表

#### 4.1 格式化日期信息
```python
date_list = []
for date, availability_list in task_list.items():
    # 对时间段按 start_time 进行排序
    availability_list.sort(key=lambda x: x["start_time"])
    
    date_obj = datetime.strptime(date, '%Y-%m-%d')
    # 获取星期几，使用中文表示
    weekday_map = {0: '周一', 1: '周二', 2: '周三', 3: '周四', 4: '周五', 5: '周六', 6: '周日'}
    weekday = weekday_map[date_obj.weekday()]
    date_str = date_obj.strftime('%m-%d') + ' ' + weekday
    
    date_list.append({
        "date_str": date_str,
        "date": date,
        "people": "",
        "selected": False,
        "canSelect": True,
        "timeSlot": "",
        "timeSlots": availability_list,
        "unavailable_reason": "",
        "meal_type_status": {}
    })
```
**作用**：
- 将时间段按开始时间排序
- 格式化日期显示（如：09-15 周一）
- 构建前端需要的数据结构
- 初始化各种状态字段

#### 4.2 日期排序
```python
date_list.sort(key=lambda x: x["date"])
```
**作用**：按日期从近到远排序。

### 第五阶段：权限检查和过滤（仅员工餐）

#### 5.1 企业晚餐权限检查
```python
if type == 'employee':
    # 获取用户关联的企业名称列表
    enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
    for enterprise_object in enterprise_objects:
        enterprise = enterprise_dao.get(db, enterprise_object.enterprise_id)
        if enterprise:
            user_enterprise_names.append(enterprise.company_name)
    
    # 检查用户的企业是否在开放列表中
    has_dinner_permission = any(enterprise_name in settings.OPEN_ENTERPRISE_LIST 
                               for enterprise_name in user_enterprise_names)
```
**作用**：
- 获取用户关联的所有企业
- 检查这些企业是否在晚餐开放列表中
- 确定用户是否有晚餐预约权限

#### 5.2 过滤晚餐时间段
```python
if not has_dinner_permission:
    for date_item in date_list:
        filtered_time_slots = []
        has_dinner_slots = False
        for time_slot in date_item["timeSlots"]:
            rule_item = rule_item_dao.get(db, time_slot["rule_item_id"])
            meal_type_rule = rule_item.meal_type if rule_item else None
            
            if meal_type_rule == RuleMealType.DINNER:
                has_dinner_slots = True
            else:
                filtered_time_slots.append(time_slot)
        
        date_item["timeSlots"] = filtered_time_slots
        if has_dinner_slots:
            date_item["meal_type_status"]["dinner"] = "no_permission"
```
**作用**：
- 如果用户没有晚餐权限，过滤掉所有晚餐时间段
- 标记餐食类型状态为无权限

### 第六阶段：现有预订过滤（仅员工餐）

#### 6.1 检查现有预订
```python
if type == 'employee' and filter_existing_reservations:
    for date_item in date_list:
        available_time_slots = []
        meal_type_blocked = {}
        
        for time_slot in date_item["timeSlots"]:
            rule_item = rule_item_dao.get(db, time_slot["rule_item_id"])
            meal_type = rule_item.meal_type if rule_item else None
            
            has_enterprise_order, reservation_period_start = order_dao.check_enterprise_order_in_day_by_meal_type(
                db, user_id, request_date_list, meal_type
            )
            
            if has_enterprise_order:
                if meal_type:
                    meal_type_blocked[meal_type.value] = "has_reservation"
            else:
                available_time_slots.append(time_slot)
```
**作用**：
- 检查用户当天是否已有企业账户支付的相同餐食类型订单
- 如果已有预订，移除对应的时间段
- 标记已有预订的餐食类型状态

#### 6.2 处理完全不可选择的日期
```python
if not available_time_slots:
    if "has_reservation" in meal_type_blocked.values():
        date_item["unavailable_reason"] = "has_reservation"
    elif "no_permission" in date_item["meal_type_status"].values():
        date_item["unavailable_reason"] = "no_permission"
    
    already_date_list.append(date_item)
```
**作用**：
- 如果某天所有时间段都被过滤掉，设置整体不可选择原因
- 将这些日期添加到 `already_date_list` 中

### 第七阶段：获取企业配置信息

#### 7.1 根据预约类型获取企业信息
```python
enterprise_list = []
if type == 'personal':
    # 个人自助餐，不需要企业信息
elif type == 'employee':
    # 获取用户关联的企业信息
    enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
    for enterprise_object in enterprise_objects:
        enterprise = enterprise_dao.get(db, enterprise_object.enterprise_id)
        enterprise_data = {
            'id': enterprise.id,
            'company_name': enterprise.company_name,
            'phone': enterprise.phone,
            'email': enterprise.email,
            'address': enterprise.address
        }
        enterprise_list.append(enterprise_data)
elif type == 'company_business':
    # 获取用户管理的企业信息
    enterprise_objects = enterprise_user_relation_dao.get_by_personal_user_id(db, user_id)
    for enterprise_object in enterprise_objects:
        if source != 'admin':
            if not enterprise_object.is_admin:
                continue
        # ... 添加企业信息
```
**作用**：根据不同的预约类型获取相应的企业配置信息。

### 第八阶段：构建并返回结果

#### 8.1 构建返回数据结构
```python
result = {
    "code": 200,
    "message": "success",
    "data": {
        "has_dinner_permission": has_dinner_permission,
        "dateList": date_list,
        "reservationFee": 0,
        "enterprise_list": enterprise_list,
        "already_date_list": already_date_list
    }
}
```

## 返回值说明

### 成功响应结构
```json
{
    "code": 200,
    "message": "success",
    "data": {
        "has_dinner_permission": true/false/null,
        "dateList": [
            {
                "date_str": "09-15 周一",
                "date": "2024-09-15",
                "people": "",
                "selected": false,
                "canSelect": true,
                "timeSlot": "",
                "timeSlots": [
                    {
                        "rule_item_id": 1,
                        "time": "11:30-14:00",
                        "start_time": "11:30",
                        "end_time": "14:00",
                        "available_count": 50,
                        "price": 68.0
                    }
                ],
                "unavailable_reason": "",
                "meal_type_status": {}
            }
        ],
        "reservationFee": 0,
        "enterprise_list": [
            {
                "id": 1,
                "company_name": "示例企业",
                "phone": "*********",
                "email": "<EMAIL>",
                "address": "企业地址"
            }
        ],
        "already_date_list": []
    }
}
```

### 字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `has_dinner_permission` | `bool/null` | 用户是否有晚餐预约权限（仅员工餐有效） |
| `dateList` | `Array` | 可预约的日期列表 |
| `reservationFee` | `int` | 预约费用（当前固定为0） |
| `enterprise_list` | `Array` | 用户关联的企业列表 |
| `already_date_list` | `Array` | 不可选择的日期列表（已有预订或无权限） |

#### dateList 中每个日期对象的字段说明

| 字段名 | 类型 | 说明 |
|--------|------|------|
| `date_str` | `str` | 格式化的日期显示（如：09-15 周一） |
| `date` | `str` | 标准日期格式（YYYY-MM-DD） |
| `timeSlots` | `Array` | 该日期的可用时间段列表 |
| `unavailable_reason` | `str` | 不可选择的原因（has_reservation/no_permission） |
| `meal_type_status` | `Object` | 按餐食类型的状态信息 |

## 业务规则总结

### 1. 餐食类型判断
- 预约类型包含 "business" → 商务餐
- 其他 → 自助餐

### 2. 权限控制（员工餐）
- **晚餐权限**：检查用户企业是否在 `OPEN_ENTERPRISE_LIST` 中
- **预订限制**：每天每种餐食类型只能有一个企业账户支付的预订

### 3. 企业配置
- **员工餐**：返回用户关联的所有企业
- **企业商务餐**：仅返回用户有管理权限的企业（source != 'admin' 时）
- **个人餐**：不返回企业信息

### 4. 时间段处理
- 按开始时间排序
- 按日期从近到远排序
- 过滤无权限或已预订的时间段

## 异常处理

方法中包含多个异常处理机制：

1. **规则处理异常**：单个规则获取失败不影响其他规则
2. **数据库查询异常**：通过 try-catch 确保方法稳定性
3. **空数据处理**：对空列表和 None 值进行适当处理

## 性能考虑

1. **数据库查询优化**：批量获取相关数据，减少数据库访问次数
2. **内存使用**：及时释放不需要的数据结构
3. **日志记录**：适度的日志记录，避免过多影响性能

## 扩展性

该方法设计考虑了良好的扩展性：

1. **新预约类型**：可通过修改类型判断逻辑支持新的预约类型
2. **新权限规则**：可在权限检查部分添加新的业务规则
3. **新餐食类型**：支持扩展新的餐食类型和相应处理逻辑

