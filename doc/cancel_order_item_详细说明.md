# cancel_order_item 方法详细说明

## 概述

`cancel_order_item` 是订单管理系统中的核心方法，用于取消订单中的单个子项。该方法支持多种支付方式的退款处理，包括微信支付、个人账户余额、企业账户余额以及混合支付等复杂场景。

## 方法签名

```python****
@router.post("/order/item/cancel")
async def cancel_order_item(
    task_info: dict, 
    event_bus: EventBusDep, 
    token: Optional[str] = Header(None), 
    db: Session = Depends(get_db)
) -> Dict[str, Any]
```

## 参数说明

| 参数名 | 类型 | 必填 | 说明 |
|--------|------|------|------|
| `task_info` | `dict` | 是 | 包含订单项ID、订单号、订单ID等信息的请求数据 |
| `event_bus` | `EventBusDep` | 是 | 事件总线依赖，用于发布订单取消事件 |
| `token` | `str` | 否 | 用户身份验证令牌，通过Header传递 |
| `db` | `Session` | 是 | 数据库会话对象 |

### task_info 结构

```json
{
    "order_item_id": 123,  // 订单项ID
    "order_no": "ORDER123456789",  // 订单号
    "order_id": 456  // 订单ID
}
```

## 详细流程分析

### 第一阶段：身份验证和参数验证

#### 1.1 用户身份验证
```python
user = WeChatUserService.verify_token(db, token)
if not user:
    raise HTTPException(status_code=401, detail={"message": "未登录", "status": 401})
```
**作用**：验证用户身份，确保只有已登录用户才能执行取消操作。

#### 1.2 参数完整性检查
```python
if "order_item_id" not in task_info or "order_no" not in task_info or "order_id" not in task_info:
    return {"message": "订单项ID不能为空", "status": 400}
```
**作用**：验证必要参数的完整性，防止空值导致的异常。

### 第二阶段：数据验证和获取

#### 2.1 订单项验证
```python
order_item = order_item_dao.get(db, order_item_id)
if not order_item:
    return {"message": "订单项不存在", "status": 404}
```
**作用**：验证订单项是否存在，防止操作不存在的数据。

#### 2.2 主订单验证
```python
order = order_dao.get(db, order_item.order_id)
if not order:
    return {"message": "订单不存在", "status": 404}
```
**作用**：验证主订单是否存在，确保数据完整性。

#### 2.3 订单类型判断
```python
is_business_dining = False
for reservation_request in order.reservation_requests:
    if hasattr(reservation_request, 'type') and reservation_request.type == ReservationType.BIZ_DINING_RESERVATION:
        is_business_dining = True
        break
```
**作用**：判断是否为商务餐订单，不同类型的订单有不同的退款策略。

### 第三阶段：退款金额计算

#### 3.1 商务餐订单退款策略
```python
if is_business_dining:
    refund_amount = order.actual_amount_paid  # 退还整个订单金额
```
**作用**：商务餐订单取消任意一项时，退还整个订单的实付金额。

#### 3.2 自助餐订单退款策略
```python
else:
    # 优先使用payable_amount或final_price
    if hasattr(order_item, 'payable_amount') and order_item.payable_amount is not None:
        refund_amount = order_item.payable_amount
    elif hasattr(order_item, 'final_price') and order_item.final_price is not None:
        refund_amount = order_item.final_price
    else:
        refund_amount = order_item.price * order_item.quantity
```
**作用**：自助餐订单只退还当前订单项的金额，支持多种价格字段的优先级选择。

### 第四阶段：权限和状态验证

#### 4.1 支付状态验证
```python
if order_item_status != OrderStatus.PAID:
    return {"message": "请勿重复申请退款", "status": 400}
```
**作用**：确保只有已支付的订单项才能申请退款，防止重复退款。

#### 4.2 权限验证
```python
if order_user_id != user_id:
    return {"message": "无权操作此订单", "status": 403}
```
**作用**：确保只有订单所有者才能取消自己的订单。

#### 4.3 预约信息验证
```python
reservation = reservation_request_dao.get_by_order_item_id_and_user_id(db, order_item_id, user_id)
if not reservation:
    return {"message": "预约信息不存在", "status": 404}
```
**作用**：获取关联的预约信息，用于后续的时间验证和状态更新。

### 第五阶段：取消时间限制验证

#### 5.1 获取取消截止时间规则
```python
rule_item = reservation.rule_item
cancellation_deadline = rule_item.cancellation_deadline
```
**作用**：获取预约规则中定义的取消截止时间。

#### 5.2 时间验证逻辑
```python
reservation_start_time = datetime.strptime('20' + reservation.reservation_period.split("_")[0], "%Y%m%d%H%M")
current_time = datetime.now()

if cancellation_deadline is not None:
    # 使用规则定义的取消截止时间
    deadline_date = reservation_start_time
    hours = cancellation_deadline // 60
    minutes = cancellation_deadline % 60
    cancellation_deadline_time = deadline_date - timedelta(hours=hours, minutes=minutes)
    
    if current_time > cancellation_deadline_time:
        return {"message": "已超过取消截止时间，无法取消预订", "status": 500}
else:
    # 使用默认取消截止时间（就餐前一天22点）
    limit_date = reservation_start_time.replace(hour=22, minute=0, second=0) - timedelta(days=1)
    if current_time > limit_date:
        return {"message": "取消失败，就餐前一天22点后不可取消预约", "status": 500}
```
**作用**：
- 如果规则项定义了取消截止时间，使用规则时间
- 否则使用默认时间（就餐前一天22点）
- 防止在不合理的时间取消预约

### 第六阶段：退款金额验证
```python
if order_actual_amount_paid < refund_amount:
    return {"message": "退款失败，总订单实付金额不足", "status": 500}
```
**作用**：确保订单实付金额足够支撑退款，防止超额退款。

### 第七阶段：事务处理和退款执行

#### 7.1 预约状态更新
```python
update_reservation_request = reservation_request_dao.cancel_reservation(db, reservation.id)
if not update_reservation_request:
    raise Exception("预约状态更新失败")
```
**作用**：将预约状态更新为已取消。

#### 7.2 退款单号生成
```python
out_refund_no = f"refund_{order_no}_{order_item_id}"
```
**作用**：生成唯一的退款单号，用于追踪退款记录。

#### 7.3 混合支付退款处理
```python
if order_is_split_payment and order_enterprise_paid_amount > 0 and order_personal_paid_amount > 0:
    success = await handle_split_payment_refund(
        db, order_id, refund_amount, out_refund_no, is_business_dining, order_item_id
    )
```
**作用**：处理混合支付（企业+个人）的复杂退款场景。

##### 7.3.1 混合支付退款详细流程
```python
# 计算各部分退款金额
enterprise_paid = order_enterprise_paid_amount
personal_paid = order_personal_paid_amount
total_paid = enterprise_paid + personal_paid

# 按比例计算退款金额
enterprise_refund_amount = round((enterprise_paid / total_paid) * total_refund_amount, 2)
personal_refund_amount = round(total_refund_amount - enterprise_refund_amount, 2)
```
**作用**：按原支付比例分配退款金额。

##### 7.3.2 企业部分退款处理
```python
# 退款到企业账户
enterprise_account.balance += refund_amount

# 创建企业退款交易记录
refund_transaction = AccountTransactionCreate(
    account_id=enterprise_account.id,
    order_id=order_id,
    transaction_type=TransactionType.REFUND,
    amount=refund_amount,
    description=f"混合支付订单退款(企业部分)：{order_id}-{order_item_id}",
    transaction_time=get_current_time()
)
```
**作用**：将企业支付部分退回到企业账户。

##### 7.3.3 个人部分退款处理
```python
# 检查个人支付方式
personal_wx_payment = wx_payment_dao.get_personal_payment_by_order_no(db, order_no)

if personal_wx_payment:
    # 个人部分是微信支付，调用微信退款
    refund_result = wechat_service.create_refund(...)
else:
    # 个人部分是账户余额支付，直接退回账户
    regular_account.balance += refund_amount
```
**作用**：根据个人支付方式选择相应的退款处理方式。

#### 7.4 纯微信支付退款处理

##### 7.4.1 商务餐微信支付退款
```python
if is_business_dining:
    # 获取所有与该订单相关的支付记录
    payment_records = wx_payment_dao.get_payments_by_order_id(db, order_id)
    
    # 按比例分配退款金额到各支付记录
    for i, payment_record in enumerate(payment_records):
        # 计算当前支付记录应退款的金额
        if i == len(payment_records) - 1:
            # 最后一个支付记录处理剩余金额，避免精度问题
            current_refund_amount_cents = refund_amount_cents - refunded_amount_cents
        else:
            # 按比例计算
            payment_amount_cents = int(payment_record.total_amount * 100)
            current_refund_amount_cents = int(refund_amount_cents * payment_amount_cents / total_payment_amount_cents)
        
        # 为每个支付记录创建独立的退款单号
        current_out_refund_no = f"{out_refund_no}_part_{i+1}"
        
        refund_result = wechat_service.create_refund(
            payment_record.transaction_id,
            current_out_refund_no,
            payment_record.total_amount,
            current_refund_amount,
            f"商务餐订单「{order_no}」退款 - 第{i+1}部分"
        )
```
**作用**：
- 商务餐可能有多次加菜支付记录
- 按比例分配退款金额到各支付记录
- 使用分（整数）计算避免浮点数精度问题
- 最后一笔支付处理剩余金额确保总额准确

##### 7.4.2 自助餐微信支付退款
```python
else:
    # 自助餐订单：原有的退款逻辑
    payment_record = wx_payment_dao.get_payment_by_order_no(db, order_no)
    
    refund_result = wechat_service.create_refund(
        payment_record.transaction_id,
        out_refund_no,
        payment_record.total_amount,
        refund_amount,
        f"订单「{order_no}」部分商品退款"
    )
```
**作用**：自助餐通常只有一次支付记录，直接退款。

#### 7.5 纯个人账户余额退款
```python
elif order_payment_method == PaymentMethod.ACCOUNT_BALANCE:
    accounts = account_dao.get_by_user_id(db, user.id)
    regular_account = next((account for account in accounts if account.type == AccountType.REGULAR), None)
    
    # 更新账户余额
    regular_account.balance += refund_amount
    
    # 创建退款交易记录
    refund_transaction = AccountTransactionCreate(
        account_id=regular_account.id,
        order_id=order_id,
        transaction_type=TransactionType.REFUND,
        amount=refund_amount,
        description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
        transaction_time=get_current_time()
    )
```
**作用**：将退款金额直接退回到用户的个人账户余额。

#### 7.6 纯企业账户余额退款
```python
elif order_payment_method == PaymentMethod.ENTERPRISE_ACCOUNT_BALANCE:
    account_transactions = account_transaction_dao.get_by_order_id(db, order_id)
    account_id = account_transactions[0].account_id
    accounts = account_dao.get_by_id(db, account_id)
    regular_account = next((account for account in accounts if account.type == AccountType.REGULAR), None)
    
    # 更新企业账户余额
    regular_account.balance += refund_amount
    
    # 创建退款交易记录
    refund_transaction = AccountTransactionCreate(
        account_id=regular_account.id,
        order_id=order_id,
        transaction_type=TransactionType.REFUND,
        amount=refund_amount,
        description=f"{'订单退款' if is_business_dining else '订单部分退款'}：{order_id}-{order_item_id}",
        transaction_time=get_current_time()
    )
```
**作用**：将退款金额退回到企业账户余额。

### 第八阶段：订单状态更新

#### 8.1 商务餐订单状态更新
```python
if is_business_dining:
    # 获取所有订单项
    order_items = order_item_dao.get_by_order(db, order_id)
    for item in order_items:
        item.status = OrderStatus.REFUNDED
    
    # 更新订单状态
    order.status = OrderStatus.REFUNDED
    order.payment_status = PaymentStatus.REFUNDED
```
**作用**：商务餐取消时，所有订单项和主订单都标记为已退款。

#### 8.2 自助餐订单状态更新
```python
else:
    # 只取消当前订单项
    order_item.status = OrderStatus.REFUNDED
```
**作用**：自助餐只更新当前订单项状态。

#### 8.3 非微信支付的订单金额更新
```python
if order_payment_method != PaymentMethod.WECHAT_PAY:
    updated_order = order_dao.get(db, order_id)
    updated_order.actual_amount_paid -= refund_amount
    
    # 检查所有订单项状态
    order_items = order_item_dao.get_by_order(db, order_id)
    all_items_refunded = all(item.status == OrderStatus.REFUNDED for item in order_items)
    
    # 更新订单状态
    updated_order.status = OrderStatus.REFUNDED if all_items_refunded else OrderStatus.REFUNDED_PARTIAL
```
**作用**：
- 更新订单实付金额
- 根据所有订单项状态决定主订单状态
- 微信支付通过回调更新，此处不处理

### 第九阶段：事务提交和事件发布

#### 9.1 事务提交
```python
db.commit()
```
**作用**：提交所有数据库更改，确保数据一致性。

#### 9.2 事件发布
```python
order_event = OrderEvent(
    action=OrderEventAction.CANCELLED,
    order_id=cancel_order_id,
    order_no=canceled_order_no,
    user_id=canceled_order_user_id,
    amount=canceled_order_amount,
    status=canceled_order_status,
    payment_status=canceled_order_payment_status,
    source="miniapp_order_cancel",
    additional_data={
        "is_business_dining": is_business_dining,
        "order_item_id": canceled_order_item_id
    }
)
await event_bus.publish(order_event)
```
**作用**：发布订单取消事件，触发相关业务逻辑（如通知、统计等）。

## 返回值说明

### 成功响应
```json
{
    "message": "取消成功",
    "status": 200
}
```

### 错误响应示例
```json
{
    "message": "未登录",
    "status": 401
}
```

```json
{
    "message": "订单项ID不能为空",
    "status": 400
}
```

```json
{
    "message": "无权操作此订单",
    "status": 403
}
```

```json
{
    "message": "订单项不存在",
    "status": 404
}
```

```json
{
    "message": "已超过取消截止时间，无法取消预订",
    "status": 500
}
```

## 业务规则总结

### 1. 订单类型差异
- **商务餐订单**：取消任意一项时退还整个订单金额，所有订单项都标记为已退款
- **自助餐订单**：只退还当前订单项金额，只更新当前订单项状态

### 2. 支付方式处理
- **微信支付**：调用微信退款接口，支持多笔支付记录的比例退款
- **个人账户余额**：直接增加用户账户余额
- **企业账户余额**：直接增加企业账户余额
- **混合支付**：按原支付比例分别处理企业和个人部分

### 3. 时间限制规则
- **规则定义时间**：优先使用预约规则中定义的取消截止时间
- **默认时间**：就餐前一天22点后不可取消
- **灵活性**：支持不同规则项的不同取消时间限制

### 4. 权限控制
- **身份验证**：必须是已登录用户
- **所有权验证**：只能取消自己的订单
- **状态验证**：只能取消已支付的订单项

### 5. 金额计算精度
- **微信退款**：使用分（整数）进行计算，避免浮点数精度问题
- **比例分配**：最后一笔处理剩余金额，确保总额准确
- **金额验证**：确保退款金额不超过实付金额

## 异常处理机制

### 1. 数据库事务
- 使用数据库事务确保操作的原子性
- 异常时自动回滚，保证数据一致性

### 2. 异常分层处理
- **参数异常**：返回400错误
- **权限异常**：返回401/403错误
- **数据异常**：返回404错误
- **业务异常**：返回500错误

### 3. 详细日志记录
- 每个关键步骤都有日志记录
- 异常时记录详细的错误信息
- 便于问题排查和系统监控

## 性能考虑

### 1. 数据库优化
- 合理使用数据库事务，避免长时间锁定
- 及时提交事务，释放数据库资源

### 2. 网络调用优化
- 微信退款接口调用的异常处理
- 避免因网络问题导致的数据不一致

### 3. 内存管理
- 及时释放不需要的对象引用
- 合理控制查询结果集大小

## 扩展性设计

### 1. 支付方式扩展
- 支持新增支付方式的退款处理
- 统一的退款接口设计

### 2. 订单类型扩展
- 支持新的订单类型和相应的退款策略
- 灵活的业务规则配置

### 3. 事件驱动架构
- 通过事件总线解耦业务逻辑
- 便于添加新的业务处理逻辑

## 安全考虑

### 1. 权限控制
- 严格的用户身份验证
- 订单所有权验证
- 防止越权操作

### 2. 数据完整性
- 退款金额验证
- 状态一致性检查
- 防止重复退款

### 3. 日志审计
- 详细的操作日志
- 便于安全审计和问题追踪

