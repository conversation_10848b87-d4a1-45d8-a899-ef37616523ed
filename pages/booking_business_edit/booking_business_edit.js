// pages/booking_business_edit/booking_business_edit.js
import { loginRequest } from "../../service/index"
import { getUserInfo } from "../../service/user"
import drawQrcode from '../../utils/weapp.qrcode.esm.js'

Page({

  /**
   * 页面的初始数据
   */
  data: {
    loading: true,
    orderId: '',
    storeInfo: {},
    categories: [],
    dishes: [],
    currentCategory: 0,
    currentCategoryDishes: [],
    cartItems: [],
    cartTotal: {
      count: 0,
      price: 0
    },
    // 新增：专门用于新增商品的统计
    newCartItems: [], // 只包含新增商品
    newCartTotal: {   // 新增商品的统计
      count: 0,
      price: 0
    },
    originalOrderTotal: 0, // 原始订单总金额
    mark_price: 0,
    user_mark: '',
    btn_mark: '下单',
    is_modified: true,
    cartVisible: false,
    showDishDetail: false,
    selectedDish: {},
    showCheckout: false,
    contactPhone: '',
    orderRemark: '',
    bookingDate: '',
    bookingTime: '',
    peopleCount: '',
    contactName: '',
    dateTimeArray: [],
    dateTimeIndex: [],
    showPaymentOptions: false,
    selectedPaymentMethod: 'wxpay',
    canUseBalance: false,
    userBalance: 0,
    payableAmount: 0,
    paymentOrderData: null,
    enterpriseList: [],
    selectedEnterprise: null,
    isEditMode: true, // 标记为编辑模式
    pageNo: 1,
    pageSize: 10,
    allDishes: [],
    hasLoadedOrderItems: false, // 添加标志位，表示已加载订单商品
    showUserInfo: false, // 控制用户信息弹窗显示
    showOrderChanges: false, // 控制订单变更摘要弹窗显示
    orderChanges: {}, // 存储订单变更摘要
    showCheckoutModal: false,
    checkoutOrderId: '',
    checkoutOrderNo: '',
    checkoutUrl: '',
    showQrcode: false,
    baseUrlHost: '', // 添加baseUrlHost
    // 新增支付相关字段
    currentItemsAmount: 0, // 当前商品总金额
    unpaidAmount: 0, // 未支付金额
    orderItems: [], // 订单商品列表
    originalPaymentMethod: '', // 原订单支付方式
    originalPayEnterpriseId: null, // 原订单支付企业ID
    // 优惠券相关数据
    showCouponList: false,  // 是否显示优惠券列表
    selectedCoupons: [],     // 选中的优惠券列表（支持多选）
    selectedCouponIds: [],   // 选中的优惠券ID列表
    couponDiscount: 0,       // 优惠券折扣金额
    finalAmount: 0,          // 使用优惠券后的最终金额
    products: [],            // 产品列表，用于优惠券计价
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 设置baseUrlHost
    this.setData({
      baseUrlHost: getApp().globalData.baseUrlHost
    });

    var orderId = '';
    console.log('options:', options);
    
    // 处理扫描二维码的情况
    if (options.q) {
      const q = decodeURIComponent(options.q);
      console.log('扫码进入，原始链接:', q);
      console.log('解码后的链接:', q);
      console.log('扫码时间:', options.scancode_time);
      
      const orderIdMatch = q.match(/[?&]order_id=([^&]*)/);
      console.log('订单ID匹配结果:', orderIdMatch);
      if (orderIdMatch && orderIdMatch[1]) {
        orderId = orderIdMatch[1];
        console.log('提取到的订单ID:', orderId);
      } else {
        console.log('未能从链接中提取到订单ID');
      }
    } else {
      orderId = options.order_id;
    }
    
    if (!orderId) {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
      return;
    }

    this.setData({
      orderId: orderId
    });

    // 设置页面标题
    wx.setNavigationBarTitle({
      title: '调整商务餐'
    });

    // 初始化日期时间选择器
    this.initDateTimePicker();
    
    // 先检查用户是否已登录
    this.checkLoginStatus(orderId);
  },

  // 检查登录状态
  checkLoginStatus(orderId) {
    const token = wx.getStorageSync('token');
    if (!token) {
      console.log('未登录，跳转到登录页');
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + orderId
      });
      return;
    }

    // 已有token，验证token是否有效
    loginRequest.post({
      url: '/auth',
      header: {
        token
      }
    }).then(res => {
      if (res.message === "已登录") {
        console.log('token有效，加载订单详情');
    // 加载订单详情
    this.loadOrderDetails(orderId);
      } else {
        console.log('token无效，需要重新登录');
        // token无效，调用app的monitor_token方法重新登录
        const app = getApp();
        if (app && app.monitor_token) {
          app.monitor_token().then(() => {
            // 重新登录成功后，检查是否获取到新token
            const newToken = wx.getStorageSync('token');
            if (newToken) {
              // 加载订单详情
              this.loadOrderDetails(orderId);
            } else {
              // 仍然没有token，跳转到登录页
              wx.navigateTo({
                url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + orderId
              });
            }
          }).catch(err => {
            console.error('重新登录失败', err);
            // 登录失败，跳转到登录页
            wx.navigateTo({
              url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + orderId
            });
          });
        } else {
          // 无法获取app实例，直接跳转到登录页
          wx.navigateTo({
            url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + orderId
          });
        }
      }
    }).catch(err => {
      console.error('验证token失败', err);
      // 验证失败，跳转到登录页
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + orderId
      });
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 页面显示时也检查登录状态
    if (this.data.orderId) {
      const token = wx.getStorageSync('token');
      if (token) {
        // 如果页面数据未加载但有token，尝试加载数据
        if (this.data.loading) {
          this.loadOrderDetails(this.data.orderId);
        }
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 初始化日期时间选择器
  initDateTimePicker() {
    const date = new Date();
    const years = [];
    const months = [];
    const days = [];
    const hours = [];
    const minutes = [];
    
    for (let i = date.getFullYear(); i <= date.getFullYear() + 5; i++) {
      years.push(i + "年");
    }
    
    for (let i = 1; i <= 12; i++) {
      months.push(i < 10 ? '0' + i + '月' : i + '月');
    }
    
    for (let i = 1; i <= 31; i++) {
      days.push(i < 10 ? '0' + i + '日' : i + '日');
    }
    // 限制时间选择范围为6-21点
    for (let i = 6; i <= 21; i++) {
      hours.push(i < 10 ? '0' + i + '时' : i + '时');
    }
    
    for (let i = 0; i < 60; i += 5) {
      minutes.push(i < 10 ? '0' + i + '分' : i + '分');
    }
    
    this.setData({
      dateTimeArray: [years, months, days, hours, minutes],
      dateTimeIndex: [0, 0, 0, 0, 0]
    });
  },

  // 加载订单详情
  loadOrderDetails(orderId) {
    wx.showLoading({
      title: '加载中...',
    });

    console.log('开始请求订单详情，orderId:', orderId); // 添加日志

    loginRequest.get({
      url: '/biz-order/detail',
      // data: { order_id: orderId, source_mark: "order_query" },
      data: { order_id: orderId },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      console.log('订单详情接口返回数据:', res); // 添加日志
      wx.hideLoading();
      
      // 处理订单已修改的情况
      if (res.status === 403) {
        wx.showModal({
          title: '提示',
          content: res.message || '订单已结账，无法查看',
          showCancel: false,
          complete: () => {
            setTimeout(() => {
              wx.reLaunch({
                url: '/pages/index/index'
              });
            }, 100);
          }
        });
        return;
      }
      
      if (res.status === 200 && res.data) {
        const orderData = res.data;
        if (orderData.user_mark == "admin") {
          this.setData({
            btn_mark: '结账'
          });
        }else{
          this.setData({
            btn_mark: '下单'
          });
        }
        this.setData({
          is_modified: orderData.is_modified
        });

        console.log('is_modified:', this.data.is_modified);
        
        // 设置订单基本信息
        this.setData({
          mark_price: orderData.total_amount,
          user_mark: orderData.user_mark,
          storeInfo: orderData.store_info || {},
          bookingDate: orderData.booking_date,
          bookingTime: orderData.booking_time,
          peopleCount: orderData.people_count,
          contactName: orderData.contact_name,
          contactPhone: orderData.contact_phone,
          orderRemark: orderData.remark || '',
          selectedEnterprise: orderData.enterprise_id || null,
          // 新增：保存原订单支付信息
          originalPaymentMethod: orderData.payment_method || '',
          originalPayEnterpriseId: orderData.pay_enterprise_id || null
        });

        // 先加载订单商品到购物车
        if (orderData.items && orderData.items.length > 0) {
          this.loadOrderItems(orderData.items);
        }
        
        // 再加载分类和菜品
        this.fetchCategories(orderData.store_info.id);
      } else {
        wx.showToast({
          title: res.message || '获取订单详情失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      console.error('获取订单详情失败，详细错误:', err); // 添加更详细的错误日志
      wx.hideLoading();
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 获取分类列表
  fetchCategories(storeId) {
    // 确保storeId是有效的整数
    const validStoreId = parseInt(storeId) || 1; // 如果storeId无效，使用默认值1
    
    loginRequest.get({
      url: '/menu/categories/search',
      data: {
        key: 'business',
        show_root: false
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200 && res.data && res.data.length > 0) {
        const categories = res.data || [];
        
        this.setData({
          categories: categories,
          loading: false
        });
        
        // 首先获取所有菜品（category_id=0），以确保能找到订单中的所有菜品
        this.fetchAllDishes(validStoreId, () => {
          // 然后获取第一个分类的菜品，用于显示
          if (categories.length > 0) {
            this.fetchDishes(categories[0].id);
          }
        });
      } else {
        this.setData({
          categories: [{ id: '0', name: '所有菜品' }],
          loading: false
        });
        
        // 获取所有菜品
        this.fetchAllDishes(validStoreId, () => {
          this.fetchDishes('0');
        });
        
        if (res.status !== 200) {
          wx.showToast({
            title: res.message || '获取分类失败',
            icon: 'none'
          });
        }
      }
    }).catch(err => {
      console.error('获取分类失败', err);
      this.setData({
        categories: [{ id: '0', name: '所有菜品' }],
        loading: false
      });
      
      // 获取所有菜品
      this.fetchAllDishes(validStoreId, () => {
        this.fetchDishes('0');
      });
      
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 获取菜品列表
  fetchDishes(categoryId, append = false, isSwitch = false) {
    // 设置默认的分页参数
    const pageNo = this.data.pageNo || 1;
    const pageSize = this.data.pageSize || 10;
    
    this.setData({
      loading: !append
    });
    
    loginRequest.get({
      url: '/menu/dishes',
      data: { 
        category_id: categoryId,
        page_no: pageNo,
        page_size: pageSize 
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      console.log('获取菜品列表成功', res); // 添加日志
      
      if (res.status === 200) {
        // 过滤掉ID为100、200的商品（补差价商品）和status为cancelled的商品
        const newDishes = (res.data.list || []).filter(dish => 
          String(dish.id) !== '100' && String(dish.id) !== '200' && dish.status !== 'cancelled'
        );
        
        console.log('过滤后的菜品列表:', newDishes);
        
        // 处理菜品数据，添加购物车中的数量和新增数量
        newDishes.forEach(dish => {
          // 首先从购物车中查找该菜品
          const cartItem = this.data.cartItems.find(item => String(item.id) === String(dish.id));
          dish.count = cartItem ? cartItem.count : 0;
          
          // 计算新增数量
          const originalItem = this.data.orderCartItems ?
            this.data.orderCartItems.find(item => String(item.id) === String(dish.id)) : null;

          if (originalItem && cartItem) {
            dish.newCount = Math.max(0, cartItem.count - originalItem.count);
          } else if (cartItem) {
            dish.newCount = cartItem.count;
          } else {
            dish.newCount = 0;
          }

          if (dish.newCount > 0) {
            console.log('菜品新增数量:', dish.id, dish.name, '新增:', dish.newCount);
          }
        });
        
        console.log('处理后的菜品数据:', newDishes.map(dish => ({id: dish.id, name: dish.name, count: dish.count, newCount: dish.newCount})));
        
        // 更新菜品列表
        if (append) {
          // 如果是加载更多，则将新菜品添加到现有菜品列表末尾
          const dishes = [...this.data.dishes, ...newDishes];
          const currentCategoryDishes = [...this.data.currentCategoryDishes, ...newDishes];
          
          this.setData({
            dishes: dishes,
            currentCategoryDishes: currentCategoryDishes,
            loading: false,
            hasMore: newDishes.length === pageSize
          });
        } else {
          // 如果不是加载更多，则直接替换现有菜品列表
          this.setData({
            dishes: isSwitch ? this.data.dishes : newDishes,
            currentCategoryDishes: newDishes,
            loading: false,
            hasMore: newDishes.length === pageSize
          });
        }
      } else {
        console.error('获取菜品列表失败', res);
        this.setData({ loading: false });
      }
    }).catch(error => {
      console.error('获取菜品列表异常', error);
      this.setData({ loading: false });
    });
  },

  // 加载订单商品到购物车
  loadOrderItems(items) {
    if (!items || items.length === 0) {
      return;
    }
    
    try {
      console.log('开始加载订单商品:', items);
      
      // 过滤掉ID为100和200的商品（补差价商品）和status为cancelled的商品
      const filteredItems = items.filter(item => 
        String(item.dish_id) !== '100' && String(item.dish_id) !== '200' && item.status !== 'cancelled'
      );
      
      console.log('过滤后的订单商品:', filteredItems);
      
      // 如果过滤后没有商品，则直接返回
      if (filteredItems.length === 0) {
        console.log('过滤后没有有效商品');
        return;
      }
      
      // 转换订单商品为购物车项
      const cartItems = filteredItems.map(item => ({
        id: item.dish_id,
        name: item.name,
        price: item.price,
        count: item.quantity,
        image: item.image || ''
      }));
      
      console.log('转换后的购物车商品:', cartItems);
      
      // 计算原始订单总金额
      let originalTotal = 0;
      cartItems.forEach(item => {
        originalTotal += item.price * item.count;
      });

      // 计算购物车总数和总价
      let totalCount = 0;
      let totalPrice = 0;
      
      cartItems.forEach(item => {
        totalCount += item.count;
        totalPrice += item.price * item.count;
      });
      
      console.log('购物车总计:', { totalCount, totalPrice });
      
      // 创建一个深拷贝，确保orderCartItems是原始订单商品的独立副本
      const orderCartItemsCopy = JSON.parse(JSON.stringify(cartItems));
      
      // 更新购物车数据，并保存订单商品信息到orderCartItems
      this.setData({
        cartItems: cartItems,
        orderCartItems: orderCartItemsCopy, // 保存原始订单商品的独立副本
        cartTotal: {
          count: totalCount,
          price: parseFloat(totalPrice.toFixed(2))
        },
        originalOrderTotal: parseFloat(originalTotal.toFixed(2)), // 保存原始订单总金额
        newCartItems: [], // 初始化时没有新增商品
        newCartTotal: {
          count: 0,
          price: 0
        },
        hasLoadedOrderItems: true // 标记已加载订单商品
      });
      
      console.log('已保存原始订单商品到orderCartItems:', orderCartItemsCopy);
      console.log('原始订单总金额:', originalTotal);
    } catch (error) {
      console.error('加载订单商品到购物车失败', error);
      wx.showToast({
        title: '加载订单商品失败',
        icon: 'none'
      });
    }
  },

  // 提交订单
  submitOrder() {
    // 验证逻辑保持不变
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + this.data.orderId
      });
      return;
    }

    // 验证必填项
    if (!this.validateOrderData()) {
      return;
    }

    // 准备订单数据
    const orderData = this.prepareOrderData();
    
    // 计算并显示订单变更摘要，然后在用户确认后提交订单
    this.calculateOrderChanges(orderData.changed_items, () => {
      this.submitOrderToServer(orderData);
    });
  },
  
  // 向服务器提交订单
  submitOrderToServer(orderData) {
    // 显示加载中
    wx.showLoading({
      title: '提交中...',
    });

    // 调用更新订单接口
    loginRequest.post({
      url: '/biz-order/update',
      data: {
        order_id: this.data.orderId,
        ...orderData
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      wx.hideLoading();
      
      if (res.status === 200) {
        // 保存订单ID和订单号
        const orderId = res.data.order_id;
        const orderNo = res.data.order_no;
        
        // 生成结算地址
        const checkoutUrl = `${getApp().globalData.baseUrlHost}/zfxcx/?order_id=${orderId}`;
        console.log('生成结算地址:', checkoutUrl);
        
        // 先显示提交成功的提示，然后延迟显示结算选择弹窗
        // wx.showToast({
        //   title: '提交成功',
        //   icon: 'success',
        //   duration: 800
        // });
        
        // 延迟1秒后显示结算选择弹窗
        setTimeout(() => {
          // 无论admin还是普通用户都弹出结账弹窗
          this.setData({
            showCheckoutModal: true,
            checkoutOrderId: orderId,
            checkoutOrderNo: orderNo,
            checkoutUrl: checkoutUrl,
            showQrcode: false
          });
        }, 200);
      } else {
        wx.showToast({
          title: res.message || '提交失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('提交订单失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 验证订单数据
  validateOrderData() {
    const phone = this.data.contactPhone;
    const phoneRegex = /^1[3-9]\d{9}$/;
    
    if (!phoneRegex.test(phone)) {
      wx.showToast({
        title: '请输入有效的手机号码',
        icon: 'none'
      });
      return false;
    }
    
    if (!this.data.bookingDate) {
      wx.showToast({
        title: '请选择预约日期',
        icon: 'none'
      });
      return false;
    }
    
    if (!this.data.bookingTime) {
      wx.showToast({
        title: '请选择预约时间',
        icon: 'none'
      });
      return false;
    }
    
    if (!this.data.peopleCount) {
      wx.showToast({
        title: '请输入人数',
        icon: 'none'
      });
      return false;
    }
    
    if (!this.data.contactName) {
      wx.showToast({
        title: '请输入姓名',
        icon: 'none'
      });
      return false;
    }

    return true;
  },

  // 准备订单数据
  prepareOrderData() {
    // 获取当前购物车中的所有商品
    const currentCartItems = this.data.cartItems.map(item => ({
      dish_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));

    // 获取原始订单中的商品（从orderCartItems中获取）
    const originalOrderItems = this.data.orderCartItems || [];
    const originalItems = originalOrderItems.map(item => ({
      dish_id: item.id,
      name: item.name,
      price: item.price,
      quantity: item.count
    }));
    
    // 添加调试日志
    console.log('原始订单商品:', JSON.stringify(originalItems));
    console.log('当前购物车商品:', JSON.stringify(currentCartItems));

    // 计算新增和修改的商品
    const changedItems = [];
    
    // 遍历当前购物车中的商品
    currentCartItems.forEach(currentItem => {
      const originalItem = originalItems.find(item => String(item.dish_id) === String(currentItem.dish_id));
      
      if (!originalItem) {
        // 如果原订单中不存在该商品，则为新增商品
        changedItems.push({
          ...currentItem,
          change_type: 'add' // 标记为新增
        });
        console.log(`新增菜品: ${currentItem.name}, 数量: ${currentItem.quantity}, 单价: ${currentItem.price}`);
      } else if (Number(currentItem.quantity) !== Number(originalItem.quantity)) {
        // 如果原订单中存在该商品但数量不同，则为修改商品
        changedItems.push({
          ...currentItem,
          change_type: 'update', // 标记为更新
          original_quantity: originalItem.quantity // 记录原始数量
        });
        console.log(`修改菜品: ${currentItem.name}, 原数量: ${originalItem.quantity}, 新数量: ${currentItem.quantity}`);
      }
      // 如果数量相同，则不需要变更
    });
    
    // 计算删除的商品（原订单中有但当前购物车中没有的商品）
    originalItems.forEach(originalItem => {
      const currentItem = currentCartItems.find(item => String(item.dish_id) === String(originalItem.dish_id));
      
      if (!currentItem) {
        // 如果当前购物车中不存在该商品，则为删除商品
        changedItems.push({
          ...originalItem,
          change_type: 'delete' // 标记为删除
        });
        console.log(`删除菜品: ${originalItem.name}, 数量: ${originalItem.quantity}`);
      }
    });

    // 输出变更汇总信息
    const addCount = changedItems.filter(item => item.change_type === 'add').length;
    const updateCount = changedItems.filter(item => item.change_type === 'update').length;
    const deleteCount = changedItems.filter(item => item.change_type === 'delete').length;
    
    console.log(`订单变更汇总: 新增${addCount}个菜品, 修改${updateCount}个菜品, 删除${deleteCount}个菜品`);
    console.log('订单变更项目详情:', changedItems);

    // 准备订单数据，包含优惠券信息
    const orderData = {
      store_id: this.data.storeInfo.id,
      contact_phone: this.data.contactPhone,
      contact_name: this.data.contactName,
      booking_date: this.data.bookingDate,
      booking_time: this.data.bookingTime,
      people_count: this.data.peopleCount,
      remark: this.data.orderRemark,
      total_amount: this.data.selectedCoupons.length > 0 ? this.data.finalAmount : this.data.cartTotal.price,
      items: currentCartItems, // 发送所有当前购物车项目
      changed_items: changedItems // 添加变更项目信息
    };

    // 如果有选中的优惠券，添加优惠券信息
    if (this.data.selectedCoupons.length > 0) {
      orderData.coupon_usage_record_ids = this.data.selectedCouponIds;
      orderData.coupon_discount = this.data.couponDiscount;
    }

    return orderData;
  },

  // 计算并显示订单变更摘要
  calculateOrderChanges(changedItems, callback) {
    if (!changedItems || changedItems.length === 0) {
      wx.showToast({
        title: '订单没有变更',
        icon: 'none'
      });
      // 如果没有变更，直接执行回调
      if (callback && typeof callback === 'function') {
        callback();
      }
      return;
    }
    
    // 分类变更项目
    const addItems = changedItems.filter(item => item.change_type === 'add');
    const updateItems = changedItems.filter(item => item.change_type === 'update');
    const deleteItems = changedItems.filter(item => item.change_type === 'delete');
    
    // 构建变更摘要
    const changes = {
      addItems,
      updateItems,
      deleteItems,
      addCount: addItems.length,
      updateCount: updateItems.length,
      deleteCount: deleteItems.length,
      totalChanges: changedItems.length
    };
    
    this.setData({
      orderChanges: changes
    });
    
    // 构建提示信息
    let message = '\n';
    
    if (addItems.length > 0) {
      message += `新增${addItems.length}个菜品\n`;
      addItems.forEach(item => {
        message += `  - ${item.name} x ${item.quantity}\n`;
      });
    }
    
    if (updateItems.length > 0) {
      message += `修改${updateItems.length}个菜品\n`;
      updateItems.forEach(item => {
        message += `  - ${item.name}: ${item.original_quantity} → ${item.quantity}\n`;
      });
    }
    
    if (deleteItems.length > 0) {
      message += `删除${deleteItems.length}个菜品\n`;
      deleteItems.forEach(item => {
        message += `  - ${item.name} x ${item.quantity}\n`;
      });
    }
    
    // 显示变更摘要
    wx.showModal({
      title: '订单变更摘要',
      content: message,
      confirmText: '确认提交',
      cancelText: '返回修改',
      success: (res) => {
        if (res.confirm) {
          // 用户点击确认，继续提交订单
          console.log('用户确认订单变更');
          if (callback && typeof callback === 'function') {
            callback();
          }
        } else {
          // 用户点击取消，返回修改
          console.log('用户取消订单变更');
          wx.hideLoading();
        }
      }
    });
  },

  // 显示/隐藏购物车
  toggleCart() {
    this.setData({
      cartVisible: !this.data.cartVisible
    });
  },

  // 添加商品到购物车
  addToCart(e) {
    const { dish } = e.currentTarget.dataset;
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => item.id === dish.id);
    
    if (existingItem) {
      existingItem.count += 1;
    } else {
      cartItems.push({
        id: dish.id,
        name: dish.name,
        price: dish.price,
        count: 1,
        image: dish.image || ''
      });
    }
    
    this.updateCart(cartItems);
  },

  // 从购物车中减少菜品
  minusDish(e) {
    const dishId = e.currentTarget.dataset.id;
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => String(item.id) === String(dishId));
    
    if (existingItem) {
      // 检查是否为原始订单中的商品
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(item => String(item.id) === String(dishId)) : null;

      if (originalItem) {
        // 如果是原始订单中的商品，不允许减少到原始数量以下
        if (existingItem.count > originalItem.count) {
          existingItem.count -= 1;
          this.updateCart(cartItems);
        } else {
          wx.showToast({
            title: '不能减少原始订单商品',
            icon: 'none'
          });
          return;
        }
      } else {
        // 如果不是原始订单中的商品，可以正常减少
        if (existingItem.count > 1) {
          existingItem.count -= 1;
        } else {
          // 如果数量为1，则从购物车中移除
          const index = cartItems.findIndex(item => String(item.id) === String(dishId));
          if (index !== -1) {
            cartItems.splice(index, 1);

            // 确保更新菜单列表中的商品数量为0
            this.updateDishCount(dishId, 0);
          }
        }
        this.updateCart(cartItems);
      }
    }
  },

  // 更新购物车数量
  updateCartItemCount(e) {
    const { dish, type } = e.currentTarget.dataset;
    const cartItems = [...this.data.cartItems];
    const item = cartItems.find(item => item.id === dish.id);
    
    if (item) {
      // 检查是否为原始订单中的商品
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(origItem => String(origItem.id) === String(dish.id)) : null;

      if (type === 'add') {
        item.count += 1;
      } else if (type === 'minus') {
        if (originalItem) {
          // 如果是原始订单中的商品，不允许减少到原始数量以下
          if (item.count > originalItem.count) {
            item.count -= 1;
          } else {
            wx.showToast({
              title: '不能减少原始订单商品',
              icon: 'none'
            });
            return;
          }
        } else {
          // 如果不是原始订单中的商品，可以正常减少
          if (item.count > 1) {
            item.count -= 1;
          }
        }
      }
    }
    
    this.updateCart(cartItems);
  },

  // 修改 updateCart 方法，同时计算菜品的新增数量
  updateCart(cartItems) {
    // 计算所有商品的总计
    let totalCount = 0;
    let totalPrice = 0;
    
    cartItems.forEach(item => {
      totalCount += item.count;
      totalPrice += item.price * item.count;
    });
    
    // 分离新增商品
    const newItems = [];
    let newTotalCount = 0;
    let newTotalPrice = 0;

    cartItems.forEach(item => {
      // 检查是否为原始订单商品
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(origItem => String(origItem.id) === String(item.id)) : null;

      if (originalItem) {
        // 如果是原始订单商品，只计算增加的数量
        if (item.count > originalItem.count) {
          const addedCount = item.count - originalItem.count;
          newItems.push({
            ...item,
            count: addedCount // 只显示新增的数量
          });
          newTotalCount += addedCount;
          newTotalPrice += item.price * addedCount;
        }
      } else {
        // 如果不是原始订单商品，完全计入新增
        newItems.push({
          ...item
        });
        newTotalCount += item.count;
        newTotalPrice += item.price * item.count;
      }
    });

    // 更新数据
    this.setData({
      cartItems,
      cartTotal: {
        count: totalCount,
        price: parseFloat(totalPrice.toFixed(2))
      },
      newCartItems: newItems,
      newCartTotal: {
        count: newTotalCount,
        price: parseFloat(newTotalPrice.toFixed(2))
      }
    }, () => {
      // 在购物车数据更新完成后，同步新增数量到菜单列表
      this.syncNewCountToMenu();
    });
  },

  // 新增：专门同步新增数量到菜单列表的方法
  syncNewCountToMenu() {
    console.log('开始同步新增数量到菜单列表');

    // 先将所有菜品的新增数量重置为0
    this.resetAllDishesNewCount();

    // 然后根据购物车计算每个菜品的新增数量
    this.data.cartItems.forEach(cartItem => {
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(origItem => String(origItem.id) === String(cartItem.id)) : null;

      let newCount = 0;
      if (originalItem) {
        // 如果是原始订单商品，计算新增的数量
        newCount = Math.max(0, cartItem.count - originalItem.count);
      } else {
        // 如果不是原始订单商品，全部算作新增
        newCount = cartItem.count;
      }

      if (newCount > 0) {
        console.log('菜品新增数量:', cartItem.id, cartItem.name, newCount);
        this.updateDishNewCount(cartItem.id, newCount);
      }
    });

    console.log('同步新增数量到菜品列表完成');
  },

  // 新增：重置所有菜品的新增数量为0
  resetAllDishesNewCount() {
    // 更新dishes数组
    const dishes = this.data.dishes.map(dish => ({
      ...dish,
      newCount: 0
    }));

    // 更新当前分类下的菜品
    const currentCategoryDishes = this.data.currentCategoryDishes.map(dish => ({
      ...dish,
      newCount: 0
    }));

    // 更新allDishes数组
    const allDishes = this.data.allDishes ? this.data.allDishes.map(dish => ({
      ...dish,
      newCount: 0
    })) : [];

    this.setData({
      dishes: dishes,
      currentCategoryDishes: currentCategoryDishes,
      allDishes: allDishes.length > 0 ? allDishes : this.data.allDishes
    });
  },

  // 新增：更新菜品新增数量
  updateDishNewCount(dishId, newCount) {
    console.log('更新菜品新增数量:', dishId, newCount);

    // 更新dishes数组中的菜品新增数量
    const dishes = this.data.dishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在dishes数组中找到菜品:', dish.id, dish.name, '更新新增数量为:', newCount);
        return { ...dish, newCount };
      }
      return dish;
    });

    // 更新当前分类下的菜品新增数量
    const currentCategoryDishes = this.data.currentCategoryDishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在currentCategoryDishes数组中找到菜品:', dish.id, dish.name, '更新新增数量为:', newCount);
        return { ...dish, newCount };
      }
      return dish;
    });

    // 更新allDishes数组中的菜品新增数量
    const allDishes = this.data.allDishes ? this.data.allDishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在allDishes数组中找到菜品:', dish.id, dish.name, '更新新增数量为:', newCount);
        return { ...dish, newCount };
      }
      return dish;
    }) : [];

    this.setData({
      dishes: dishes,
      currentCategoryDishes: currentCategoryDishes,
      allDishes: allDishes.length > 0 ? allDishes : this.data.allDishes
    });
  },

  // 添加分类切换方法
  switchCategory(e) {
    const index = e.currentTarget.dataset.index;
    const categoryId = this.data.categories[index].id;
    
    this.setData({
      currentCategory: index,
      pageNo: 1  // 切换分类时重置分页
    });
    
    // 获取该分类下的菜品
    this.fetchDishes(categoryId, false, true); // 添加第三个参数，表示这是切换分类
  },

  // 添加菜品点击方法
  showDishDetail(e) {
    const { dish } = e.currentTarget.dataset;
    this.setData({
      selectedDish: dish,
      showDishDetail: true
    });
  },

  // 关闭菜品详情
  closeDishDetail() {
    this.setData({
      showDishDetail: false
    });
  },

  // 添加菜品到购物车
  addDish(e) {
    const dishId = e.currentTarget.dataset.id;
    const dish = this.data.dishes.find(d => String(d.id) === String(dishId)) || 
                 this.data.currentCategoryDishes.find(d => String(d.id) === String(dishId)) ||
                 (this.data.allDishes ? this.data.allDishes.find(d => String(d.id) === String(dishId)) : null);
    
    if (!dish) {
      console.error('未找到菜品:', dishId);
      return;
    }
    
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => String(item.id) === String(dish.id));
    
    if (existingItem) {
      existingItem.count += 1;
    } else {
      cartItems.push({
        id: dish.id,
        name: dish.name,
        price: dish.price,
        count: 1,
        image: dish.image || ''
      });
    }
    
    this.updateCart(cartItems);
  },

  // 更新菜品数量
  updateDishCount(dishId, count) {
    console.log('更新菜品数量:', dishId, count);
    
    // 更新dishes数组中的菜品数量
    const dishes = this.data.dishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在dishes数组中找到菜品:', dish.id, dish.name, '更新数量为:', count);
        return { ...dish, count };
      }
      return dish;
    });
    
    // 更新当前分类下的菜品数量
    const currentCategoryDishes = this.data.currentCategoryDishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在currentCategoryDishes数组中找到菜品:', dish.id, dish.name, '更新数量为:', count);
        return { ...dish, count };
      }
      return dish;
    });
    
    // 更新allDishes数组中的菜品数量
    const allDishes = this.data.allDishes ? this.data.allDishes.map(dish => {
      if (String(dish.id) === String(dishId)) {
        console.log('在allDishes数组中找到菜品:', dish.id, dish.name, '更新数量为:', count);
        return { ...dish, count };
      }
      return dish;
    }) : [];
    
    // 检查是否有任何菜品被更新
    const updatedDish = dishes.find(dish => String(dish.id) === String(dishId) && dish.count === count);
    const updatedCurrentDish = currentCategoryDishes.find(dish => String(dish.id) === String(dishId) && dish.count === count);
    
    if (updatedDish || updatedCurrentDish) {
      console.log('菜品数量更新成功');
      this.setData({
        dishes: dishes,
        currentCategoryDishes: currentCategoryDishes,
        allDishes: allDishes.length > 0 ? allDishes : this.data.allDishes
      });
    } else {
      console.log('未找到需要更新数量的菜品:', dishId);
    }
  },

  // 处理日期时间选择
  bindDateTimeChange(e) {
    const val = e.detail.value;
    const dateTimeArray = this.data.dateTimeArray;
    
    const year = dateTimeArray[0][val[0]].replace('年', '');
    const month = dateTimeArray[1][val[1]].replace('月', '');
    const day = dateTimeArray[2][val[2]].replace('日', '');
    const hour = dateTimeArray[3][val[3]].replace('时', '');
    const minute = dateTimeArray[4][val[4]].replace('分', '');
    
    const bookingDate = `${year}年${month}月${day}日`;
    const bookingTime = `${hour}时${minute}分`;
    
    this.setData({
      dateTimeIndex: val,
      bookingDate,
      bookingTime
    });
  },

  // 处理输入框变化
  handleInput(e) {
    const { field } = e.currentTarget.dataset;
    const value = e.detail.value;
    
    this.setData({
      [field]: value
    });
  },

  // 清空购物车
  clearCart() {
    // 注意：不要修改orderCartItems，确保它保持原始订单数据
    this.setData({
      cartItems: [],
      cartTotal: {
        count: 0,
        price: 0
      }
    });
    
    // 更新所有菜品的数量为0
    const dishes = this.data.dishes.map(dish => ({
      ...dish,
      count: 0
    }));
    
    const currentCategoryDishes = this.data.currentCategoryDishes.map(dish => ({
      ...dish,
      count: 0
    }));
    
    // 更新allDishes中的菜品数量为0
    const allDishes = this.data.allDishes ? this.data.allDishes.map(dish => ({
      ...dish,
      count: 0
    })) : [];
    
    this.setData({
      dishes,
      currentCategoryDishes,
      allDishes: allDishes.length > 0 ? allDishes : this.data.allDishes
    });
    
    console.log('清空购物车，但保留原始订单数据', this.data.orderCartItems);
  },

  // 添加在菜品详情弹窗中添加菜品的方法
  addDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    const dish = this.data.dishes.find(d => String(d.id) === String(dishId)) ||
                 this.data.currentCategoryDishes.find(d => String(d.id) === String(dishId));
    
    if (!dish) {
      console.error('未找到菜品:', dishId);
      return;
    }
    
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => String(item.id) === String(dish.id));
    
    if (existingItem) {
      existingItem.count += 1;
    } else {
      cartItems.push({
        id: dish.id,
        name: dish.name,
        price: dish.price,
        count: 1,
        image: dish.image || ''
      });
    }
    
    // 更新购物车数据
    this.updateCart(cartItems);
    
    // 计算并更新弹窗中的新增数量
    const originalItem = this.data.orderCartItems ?
      this.data.orderCartItems.find(item => String(item.id) === String(dish.id)) : null;

    let newCount = 0;
    if (originalItem) {
      newCount = Math.max(0, (existingItem ? existingItem.count + 1 : 1) - originalItem.count);
    } else {
      newCount = existingItem ? existingItem.count + 1 : 1;
    }

    // 同时更新菜品详情弹窗中的数量
    this.setData({
      selectedDish: {
        ...this.data.selectedDish,
        count: existingItem ? existingItem.count + 1 : 1,
        newCount: newCount
      }
    });
  },

  // 添加在菜品详情弹窗中减少菜品的方法
  minusDishInModal(e) {
    const dishId = e.currentTarget.dataset.id;
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => String(item.id) === String(dishId));
    
    if (existingItem) {
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(item => String(item.id) === String(dishId)) : null;

      if (originalItem) {
        // 如果是原始订单中的商品，只减少新增的部分
        if (existingItem.count > originalItem.count) {
          existingItem.count -= 1;
          this.updateCart(cartItems);

          // 计算新的新增数量
          const newCount = Math.max(0, existingItem.count - originalItem.count);

          // 更新弹窗中的菜品数量
          this.setData({
            selectedDish: {
              ...this.data.selectedDish,
              count: existingItem.count,
              newCount: newCount
            }
          });
        }
      } else {
        // 如果不是原始订单中的商品，可以正常减少
        if (existingItem.count > 1) {
          existingItem.count -= 1;
          this.updateCart(cartItems);

          // 更新弹窗中的菜品数量
          this.setData({
            selectedDish: {
              ...this.data.selectedDish,
              count: existingItem.count,
              newCount: existingItem.count
            }
          });
        } else {
          // 如果数量为1，则从购物车移除
          const newCartItems = cartItems.filter(item => String(item.id) !== String(dishId));
          this.updateCart(newCartItems);

          // 更新弹窗中的菜品数量
          this.setData({
            selectedDish: {
              ...this.data.selectedDish,
              count: 0,
              newCount: 0
            }
          });
        }
      }
    }
  },

  // 同步购物车数据到菜品列表
  syncCartItemsToMenu() {
    // 现在直接调用新的同步新增数量的方法
    this.syncNewCountToMenu();
  },

  // 获取所有菜品
  fetchAllDishes(storeId, callback) {
    console.log('开始获取所有菜品');
    
    loginRequest.get({
      url: '/menu/dishes',
      data: { 
        category_id: '0', // 0表示获取所有菜品
        page_no: 1,
        page_size: 100 // 设置一个较大的值，以获取所有菜品
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      console.log('获取所有菜品成功', res);
      
      if (res.status === 200) {
        // 过滤掉ID为100和200的商品（补差价商品）
        const allDishes = (res.data.list || []).filter(dish => 
          String(dish.id) !== '100' && String(dish.id) !== '200'
        );
        
        console.log('过滤后的所有菜品:', allDishes);
        
        // 处理菜品数据，添加购物车中的数量和新增数量
        allDishes.forEach(dish => {
          // 首先从购物车中查找该菜品
          const cartItem = this.data.cartItems.find(item => String(item.id) === String(dish.id));
          dish.count = cartItem ? cartItem.count : 0;
          
          // 计算新增数量
          const originalItem = this.data.orderCartItems ?
            this.data.orderCartItems.find(item => String(item.id) === String(dish.id)) : null;

          if (originalItem && cartItem) {
            dish.newCount = Math.max(0, cartItem.count - originalItem.count);
          } else if (cartItem) {
            dish.newCount = cartItem.count;
          } else {
            dish.newCount = 0;
          }

          if (dish.newCount > 0) {
            console.log('在所有菜品中找到新增菜品:', dish.id, dish.name, '新增数量:', dish.newCount);
          }
        });
        
        // 保存所有菜品数据
        this.setData({
          allDishes: allDishes,
          loading: false
        }, () => {
          console.log('所有菜品数据保存完成');
          
          // 如果有回调函数，则执行
          if (typeof callback === 'function') {
            callback();
          }
        });
      } else {
        console.error('获取所有菜品失败', res);
        this.setData({ loading: false });
        
        if (typeof callback === 'function') {
          callback();
        }
      }
    }).catch(error => {
      console.error('获取所有菜品异常', error);
      this.setData({ loading: false });
      
      if (typeof callback === 'function') {
        callback();
      }
    });
  },

  // 同步购物车数据到菜品列表
  syncCartItemsToAllDishes() {
    if (!this.data.cartItems || this.data.cartItems.length === 0) {
      console.log('购物车为空，无需同步到菜品列表');
      return;
    }
    
    console.log('开始同步购物车数据到菜品列表，购物车商品数量:', this.data.cartItems.length);
    console.log('购物车商品详情:', JSON.stringify(this.data.cartItems));
    console.log('菜品列表商品数量:', this.data.dishes.length);
    
    // 遍历购物车中的每个商品，更新菜品列表中对应的数量
    this.data.cartItems.forEach(cartItem => {
      if (cartItem.count > 0) {
        console.log('更新菜品数量:', cartItem.id, cartItem.name, cartItem.count);
        
        // 检查菜品列表中是否存在该菜品
        const dishInMenu = this.data.dishes.find(dish => dish.id === cartItem.id);
        if (dishInMenu) {
          console.log('在菜品列表中找到菜品:', dishInMenu.id, dishInMenu.name);
        } else {
          console.log('在菜品列表中未找到菜品:', cartItem.id, cartItem.name);
        }
        
        this.updateDishCount(cartItem.id, cartItem.count);
      }
    });
    
    console.log('同步购物车数据到菜品列表完成');
  },

  // 从购物车移除商品
  removeFromCart(e) {
    const { dish } = e.currentTarget.dataset;
    const cartItems = this.data.cartItems.filter(item => String(item.id) !== String(dish.id));
    this.updateCart(cartItems);
    
    // 确保更新菜单列表中的商品数量为0
    this.updateDishCount(dish.id, 0);
  },

  // 显示用户信息弹窗
  showUserInfoModal() {
    this.setData({
      showUserInfo: true
    });
  },

  // 隐藏用户信息弹窗
  hideUserInfoModal() {
    this.setData({
      showUserInfo: false
    });
  },
  
  // 直接去结算，跳过结算页面，直接提交订单
  goToCheckout() {
    console.log('直接去结算，跳过结算页面');
    console.log(this.data.user_mark);
    
    // 检查当前时间是否在9点半之前（仅对当天的订单进行限制）
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute; // 转换为分钟
    const cutoffTime = 9 * 60 + 30; // 9点半 = 9*60+30 = 570分钟

    // 获取当前日期，格式：YYYY年MM月DD日
    const currentYear = now.getFullYear();
    const currentMonth = String(now.getMonth() + 1).padStart(2, '0');
    const currentDay = String(now.getDate()).padStart(2, '0');
    const currentDateStr = `${currentYear}年${currentMonth}月${currentDay}日`;

    console.log('当前时间:', currentHour + ':' + currentMinute);
    console.log('当前时间(分钟):', currentTime);
    console.log('截止时间(分钟):', cutoffTime);
    console.log('当前日期:', currentDateStr);
    console.log('订单日期:', this.data.bookingDate);

    // 解析订单日期，格式：YYYY年MM月DD日
    const orderDateMatch = this.data.bookingDate.match(/(\d{4})年(\d{2})月(\d{2})日/);
    if (orderDateMatch) {
      const orderYear = parseInt(orderDateMatch[1]);
      const orderMonth = parseInt(orderDateMatch[2]);
      const orderDay = parseInt(orderDateMatch[3]);

      // 创建订单日期对象
      const orderDate = new Date(orderYear, orderMonth - 1, orderDay);
      const currentDate = new Date(currentYear, parseInt(currentMonth) - 1, parseInt(currentDay));

      console.log('订单日期对象:', orderDate);
      console.log('当前日期对象:', currentDate);

      // 订单日期早于或等于当天，且时间超过9:30才进行限制
      if (orderDate <= currentDate && currentTime >= cutoffTime && this.data.user_mark !== "admin") {
        wx.showToast({
          title: '当前时间已超过9:30，如需修改订单请联系服务员！',
          icon: 'none'
        });
        return;
      }
    }

    // 直接调用提交订单方法
    this.submitOrder();
  },

  // 处理滚动到底部加载更多菜品
  loadMoreDishes() {
    const currentCategory = this.data.currentCategory;
    const categoryId = this.data.categories[currentCategory]?.id;
    
    if (!categoryId) return;
    
    // 增加页码并加载更多菜品
    const nextPage = this.data.pageNo + 1;
    this.setData({ pageNo: nextPage });
    
    this.fetchDishes(categoryId, true); // 传入true表示追加模式
  },

  // 添加生成二维码的方法
  generateQrcode() {
    if (this.data.user_mark !== 'admin') {
      // 非admin直接跳转支付页面
      //wx.redirectTo({
      //  url: `/pages/booking_business_pay/booking_business_pay?order_id=${this.data.checkoutOrderId}`
      //});

      // 非admin直接在当前页面显示支付选项
      this.showPaymentOptionsInPage();
      this.setData({ showCheckoutModal: false });
      return;
    }
    // admin 生成二维码
    this.setData({
      showQrcode: true
    });
    
    // 使用微信小程序的 drawQrcode 方法生成二维码
    const options = {
      width: 200,
      height: 200,
      canvasId: 'qrcode-canvas',
      x: 0,
      y: 0,
      text: this.data.checkoutUrl,
      callback: (res) => {
        console.log('二维码生成成功');
      }
    }
    
    drawQrcode(options);
  },

  // 关闭结算弹窗
  closeCheckoutModal() {
    this.setData({
      showCheckoutModal: false
    });
    
    // 如果是admin用户，不恢复订单状态，因为客户有可能后面才买单
    if (this.data.user_mark !== 'admin') {
      this.restoreOriginalOrder();
    }
  },

  // 继续购物
  continueShopping() {
    this.setData({
      showCheckoutModal: false
    });
    
    // 恢复到原始订单状态
    this.restoreOriginalOrder();
  },

  // 添加拨打电话功能
  makePhoneCall() {
    wx.makePhoneCall({
      phoneNumber: '18802053864',
      success: () => {
        console.log('拨打电话成功');
      },
      fail: (err) => {
        console.error('拨打电话失败', err);
      }
    });
  },

  // 新增：清空新增商品的方法
  clearNewItems() {
    wx.showModal({
      title: '确认清空',
      content: '确定要清空所有新增商品吗？',
      success: (res) => {
        if (res.confirm) {
          // 恢复到原始订单状态
          const originalItems = this.data.orderCartItems || [];
          this.setData({
            cartItems: [...originalItems],
            cartTotal: {
              count: originalItems.reduce((sum, item) => sum + item.count, 0),
              price: this.data.originalOrderTotal
            },
            newCartItems: [],
            newCartTotal: {
              count: 0,
              price: 0
            }
          });

          // 重置所有菜品的新增数量为0
          this.resetAllDishesNewCount();
        }
      }
    });
  },

  // 新增：专门处理新增商品减少的方法
  minusNewItem(e) {
    const dishId = e.currentTarget.dataset.id;
    const cartItems = [...this.data.cartItems];
    const existingItem = cartItems.find(item => String(item.id) === String(dishId));

    if (existingItem) {
      const originalItem = this.data.orderCartItems ?
        this.data.orderCartItems.find(item => String(item.id) === String(dishId)) : null;

      if (originalItem) {
        // 如果是原始订单中的商品，只减少新增的部分
        if (existingItem.count > originalItem.count) {
          existingItem.count -= 1;
          this.updateCart(cartItems);
        }
      } else {
        // 如果是全新商品，可以正常减少
        if (existingItem.count > 1) {
          existingItem.count -= 1;
        } else {
          // 移除商品
          const index = cartItems.findIndex(item => String(item.id) === String(dishId));
          if (index !== -1) {
            cartItems.splice(index, 1);
          }
        }
        this.updateCart(cartItems);
      }
    }
  },

  // 优惠券相关方法
  // 生成产品数量列表
  generateProductList() {
    const products = [];

    // 从cartItems中提取产品信息
    if (this.data.cartItems && this.data.cartItems.length > 0) {
      this.data.cartItems.forEach(item => {
        if (item.id && item.count > 0) {
          products.push({
            product_id: item.id,
            quantity: item.count
          });
        }
      });
    }

    return products;
  },

  /**
   * 显示优惠券列表
   */
  showCouponList() {
    // 检查是否有购物车商品
    if (!this.data.cartItems || this.data.cartItems.length === 0) {
      wx.showToast({
        title: '请先选择商品',
        icon: 'none'
      });
      return;
    }

    // 确保用户信息已加载
    const app = getApp();
    if (!app.globalData.userInfo || !app.globalData.userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败，请重新登录',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    if (products.length === 0) {
      wx.showToast({
        title: '暂无可用产品信息',
        icon: 'none'
      });
      return;
    }

    this.setData({
      showCouponList: true,
      products: products  // 保存产品列表供组件使用
    });
  },

  /**
   * 隐藏优惠券列表
   */
  hideCouponList() {
    this.setData({
      showCouponList: false
    });
  },

  /**
   * 选择优惠券
   */
  onCouponSelect(e) {
    const { couponIds, coupons } = e.detail;

    if (coupons && coupons.length > 0) {
      // 调用后端API计算准确的优惠金额
      this.calculateCouponPricing(couponIds, coupons);
    } else {
      // 取消选择所有优惠券
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [], // 清空选中的优惠券ID列表
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price,
        showCouponList: false
      });

      wx.showToast({
        title: '已取消选择',
        icon: 'none'
      });
    }
  },

  // 调用后端API计算优惠券价格
  calculateCouponPricing(couponIds, coupons) {
    const app = getApp();
    const userInfo = app.globalData.userInfo;

    if (!userInfo || !userInfo.id) {
      wx.showToast({
        title: '用户信息获取失败',
        icon: 'none'
      });
      return;
    }

    // 生成产品数量列表
    const products = this.generateProductList();

    // 如果没有优惠券，直接重置状态
    if (!couponIds || couponIds.length === 0) {
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });
      return;
    }

    const requestData = {
      user_id: userInfo.id,
      products: products,
      coupon_usage_record_ids: couponIds
    };

    wx.showLoading({
      title: '计算优惠中...'
    });

    loginRequest.post({
      url: '/coupon/calculate-pricing',
      data: requestData,
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      wx.hideLoading();

      if (res.status === 200 && res.data) {
        const pricingData = res.data;
        console.log('优惠券计价结果:', pricingData);

        // 处理优惠券数据，添加折扣金额信息
        const couponsWithDiscount = coupons.map(coupon => {
          const pricingInfo = pricingData.coupon_pricing_details.find(
            detail => detail.coupon_usage_record_id === coupon.coupon_usage_record_id
          );

          return {
            ...coupon,
            discountAmount: pricingInfo ? pricingInfo.discount_amount : 0,
            displayText: this.formatCouponDisplayText(coupon.coupon, pricingInfo ? pricingInfo.discount_amount : 0)
          };
        });

        // 计算总优惠金额
        const totalDiscount = pricingData.total_discount_amount || 0;

        // 修正总计金额计算：小计 - 优惠金额
        const finalAmount = Math.max(0, this.data.cartTotal.price - totalDiscount);
        console.log('计算优惠券价格 - cartTotal.price:', this.data.cartTotal.price);
        console.log('计算优惠券价格 - finalAmount:', finalAmount);
        console.log('计算优惠券价格 - totalDiscount:', totalDiscount);
        this.setData({
          selectedCoupons: couponsWithDiscount,
          selectedCouponIds: couponIds,
          couponDiscount: totalDiscount,
          finalAmount: finalAmount,
          showCouponList: false
        });

        console.log('设置后的数据状态:', {
          selectedCoupons: this.data.selectedCoupons.length,
          finalAmount: this.data.finalAmount,
          cartTotal: this.data.cartTotal.price
        });

        wx.showToast({
          title: `已选择${coupons.length}张优惠券，优惠¥${totalDiscount}`,
          icon: 'success'
        });
      } else {
        console.error('计算优惠券价格失败:', res);
        wx.showToast({
          title: res.message || '计算优惠失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('计算优惠券价格异常:', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 取消单张优惠券
  cancelCoupon(e) {
    const couponId = e.currentTarget.dataset.couponId;
    console.log('取消优惠券:', couponId);

    if (!couponId) {
      return;
    }

    // 从已选择的优惠券中移除指定的优惠券
    const updatedCoupons = this.data.selectedCoupons.filter(coupon =>
      coupon.coupon_usage_record_id !== couponId
    );

    const updatedCouponIds = this.data.selectedCouponIds.filter(id =>
      id !== couponId.toString()
    );

    if (updatedCoupons.length === 0) {
      // 如果没有剩余优惠券，直接清空
      this.setData({
        selectedCoupons: [],
        selectedCouponIds: [],
        couponDiscount: 0,
        finalAmount: this.data.cartTotal.price
      });

      wx.showToast({
        title: '已取消所有优惠券',
        icon: 'none'
      });
    } else {
      // 重新计算剩余优惠券的价格
      this.calculateCouponPricing(updatedCouponIds, updatedCoupons);

      wx.showToast({
        title: '已取消该优惠券',
        icon: 'none'
      });
    }
  },

  // 清空所有优惠券
  clearAllCoupons() {
    this.setData({
      selectedCoupons: [],
      selectedCouponIds: [],
      couponDiscount: 0,
      finalAmount: this.data.cartTotal.price
    });

    wx.showToast({
      title: '已清空所有优惠券',
      icon: 'none'
    });
  },

  // 格式化优惠券显示文本
  formatCouponDisplayText(coupon, discountAmount) {
    if (coupon.type === 'DISCOUNT') {
      return `满${coupon.condition_amount}减${coupon.quantity}`;
    } else if (coupon.type === 'PERCENTAGE') {
      const percentage = Math.round(coupon.quantity * 100);
      return `满${coupon.condition_amount}享${percentage}折`;
    } else if (coupon.type === 'FREE_SHIPPING') {
      return '免运费券';
    }
    return '优惠券';
  },

  /**
   * 计算优惠券折扣金额
   */
  calculateCouponDiscount(couponData) {
    const coupon = couponData.coupon;
    const orderAmount = this.data.cartTotal.price;

    // 检查金额条件
    if (coupon.condition_amount > 0 && orderAmount < coupon.condition_amount) {
      return 0;
    }

    if (coupon.type === 'DISCOUNT') {
      return Math.min(coupon.quantity, orderAmount);
    } else if (coupon.type === 'PERCENTAGE') {
      return orderAmount * coupon.quantity;
    } else if (coupon.type === 'FREE_SHIPPING') {
      // 免运费券的处理逻辑
      return 0; // 实际应用中需要根据具体业务逻辑计算
    }

    return 0;
  },

  // 检查是否为原始订单商品且数量已达到最小值（不能减少）
  isOriginalItemMinDisabled(item) {
    const originalItem = this.data.orderCartItems ?
      this.data.orderCartItems.find(origItem => String(origItem.id) === String(item.id)) : null;

    if (originalItem) {
      return item.count <= originalItem.count;
    }
    return false;
  },

  // 新增：在当前页面显示支付选项
  showPaymentOptionsInPage() {
    // 首先检查 user_mark 值
    if (!this.data.user_mark || (this.data.user_mark !== 'enterprise' && this.data.user_mark !== 'user' && this.data.user_mark !== 'admin')) {
      wx.showToast({
        title: '非该订单公司员工，暂时不提供支付账单功能！',
        icon: 'none',
        duration: 2000
      });
      return;
    }

    // 首先加载订单详情来获取支付相关信息
    this.loadOrderPaymentInfo();
  },

  // 加载订单支付信息
  loadOrderPaymentInfo() {
    wx.showLoading({
      title: '加载支付信息...',
    });

    loginRequest.get({
      url: '/biz-order/detail',
      data: { order_id: this.data.checkoutOrderId, source_mark: "order_query" },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      wx.hideLoading();

      if (res.status === 200 && res.data) {
        const orderData = res.data;

        // 过滤掉ID为100、200的商品和status为cancelled的商品
        const filteredItems = (orderData.items || []).filter(item =>
          String(item.dish_id) !== '100' && String(item.dish_id) !== '200' && item.status !== 'cancelled'
        );

        // 计算当前商品总金额
        const currentItemsAmount = filteredItems.reduce((total, item) => {
          return total + (item.price * item.quantity);
        }, 0);

        // 计算未支付金额（当前商品总金额减去已支付金额）
        const totalAmount = orderData.total_amount || 0;
        let unpaidAmount = currentItemsAmount - totalAmount;
        // 使用toFixed修复浮点数精度问题，然后转回数字类型
        unpaidAmount = parseFloat((unpaidAmount > 0 ? unpaidAmount : 0).toFixed(2));

        // 更新支付相关数据
        this.setData({
          currentItemsAmount: currentItemsAmount,
          unpaidAmount: unpaidAmount,
          orderItems: filteredItems,
          payableAmount: unpaidAmount
        });

        // 在获取支付信息前先获取最新的用户余额和企业列表
        this.getUserBalance();
        this.getEnterpriseList();

        // 显示支付选项前，根据原订单支付方式设置默认选项
        let defaultPaymentMethod = 'wxpay';

        // 根据原订单支付方式确定默认选项
        if (this.data.originalPaymentMethod === 'wechat_pay') {
          defaultPaymentMethod = 'wxpay';
        } else if (this.data.originalPaymentMethod === 'account_balance') { // 修改这里
          defaultPaymentMethod = 'balance';
        } else if (this.data.originalPaymentMethod === 'biz_enterprise' || this.data.originalPayEnterpriseId) {
          defaultPaymentMethod = 'biz_enterprise';
        }

        // 如果支付金额为0，默认选择余额支付
        if (unpaidAmount === 0) {
          defaultPaymentMethod = 'balance';
        }

        // 显示支付选项
        this.setData({
          showPaymentOptions: true,
          selectedPaymentMethod: defaultPaymentMethod
        });
      } else {
        wx.showToast({
          title: res.message || '获取支付信息失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('获取支付信息失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 获取用户余额
  getUserBalance() {
    loginRequest.get({
      url: '/account/balance',
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        const balance = res.data.balance || 0;
        const canUseBalance = balance >= this.data.unpaidAmount;

        // 同样更新全局用户信息
        const app = getApp();
        if (app.globalData.userInfo) {
          app.globalData.userInfo.balance = balance;
        }

        this.setData({
          userBalance: balance,
          canUseBalance: canUseBalance
        });
      }
    }).catch(err => {
      console.error('获取用户余额失败', err);
    });
  },

  // 获取企业列表
  getEnterpriseList() {
    loginRequest.get({
      url: '/enterprise/list',
      data: {
        order_id: this.data.checkoutOrderId
      },
      header: {
        'token': wx.getStorageSync('token')
      }
    }).then(res => {
      if (res.status === 200) {
        this.setData({
          enterpriseList: res.data || []
        });

        // 如果企业列表为空，则隐藏企业支付选项
        if (!res.data || res.data.length === 0) {
          // 如果当前选择的是企业支付，则切换到微信支付
          if (this.data.selectedPaymentMethod === 'biz_enterprise') {
            this.setData({
              selectedPaymentMethod: 'wxpay'
            });
          }
        }
      }
    }).catch(err => {
      console.error('获取企业列表失败', err);
    });
  },

  // 关闭支付选项
  closePayment() {
    this.setData({
      showPaymentOptions: false
    });

    // 恢复到原始订单状态
    this.restoreOriginalOrder();
  },

  // 选择支付方式
  selectPaymentMethod(e) {
    const method = e.currentTarget.dataset.method;
    this.setData({
      selectedPaymentMethod: method
    });
  },

  // 选择企业
  selectEnterprise(e) {
    const enterpriseId = e.currentTarget.dataset.id;
    this.setData({
      selectedEnterprise: enterpriseId
    });
  },

  /**
   * 刷新用户余额和相关信息
   */
  refreshUserInfoAndBookings() {
    const app = getApp();

    // 使用service/user.js中的getUserInfo方法刷新用户信息
    getUserInfo().then(userInfo => {
      // 更新全局用户信息
      if (app.globalData.userInfo) {
        app.globalData.userInfo.balance = userInfo.balance || 0;
      }

      // 更新页面数据
      const canUseBalance = (userInfo.balance || 0) >= this.data.unpaidAmount;
      this.setData({
        userBalance: userInfo.balance || 0,
        canUseBalance: canUseBalance
      });
    }).catch(err => {
      console.error('获取用户信息失败', err);
      // 即使获取用户信息失败，也不应该影响支付成功的流程
    });
  },

  confirmPayment() {
    const token = wx.getStorageSync('token');
    if (!token) {
      wx.navigateTo({
        url: '/pages/phoneAuth/phoneAuth?redirect=booking_business_edit&order_id=' + this.data.orderId
      });
      return;
    }

    const paymentMethod = this.data.selectedPaymentMethod;

    // 企业支付需要选择企业
    if (paymentMethod === 'biz_enterprise' && !this.data.selectedEnterprise) {
      wx.showToast({
        title: '请选择企业',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({
      title: '处理中...'
    });

    // 调用支付接口
    loginRequest.post({
      url: '/biz-order/pay',
      data: {
        order_id: this.data.checkoutOrderId,
        payment_method: paymentMethod,
        enterprise_id: paymentMethod === 'biz_enterprise' ? this.data.selectedEnterprise : undefined
      },
      header: {
        'token': token
      }
    }).then(res => {
      wx.hideLoading();

      if (res.status === 200) {
        if (paymentMethod === 'wxpay') {
          // 微信支付需要调起支付
          const payData = res.data;
          this.callWxPay(payData);
        } else {
          // 其他支付方式直接显示成功
          wx.showToast({
            title: '支付成功',
            icon: 'success'
          });

          // 支付成功后更新用户余额数据 - 使用新的方法
          this.refreshUserInfoAndBookings();

          // 关闭支付选项
          this.setData({
            showPaymentOptions: false
          });

          setTimeout(() => {
            // 返回上一页并刷新
            const pages = getCurrentPages();
            const prevPage = pages[pages.length - 2];
            if (prevPage) {
              prevPage.onShow(); // 触发上一页的刷新
            }
            wx.navigateBack();
          }, 1500);
        }
      } else {
        wx.showToast({
          title: res.message || '支付失败',
          icon: 'none'
        });
      }
    }).catch(err => {
      wx.hideLoading();
      console.error('支付失败', err);
      wx.showToast({
        title: '网络错误，请重试',
        icon: 'none'
      });
    });
  },

  // 调用微信支付
  callWxPay(payData) {
    // 显示支付提示
    wx.showLoading({
      title: '正在调起支付...',
      mask: true
    });

    wx.requestPayment({
      timeStamp: payData.timeStamp,
      nonceStr: payData.nonceStr,
      package: payData.package,
      signType: payData.signType,
      paySign: payData.paySign,
      success: (res) => {
        wx.hideLoading();
        wx.showToast({
          title: '支付成功',
          icon: 'success'
        });

        // 支付成功后更新用户余额数据 - 使用新的方法
        this.refreshUserInfoAndBookings();

        // 关闭支付选项
        this.setData({
          showPaymentOptions: false
        });

        setTimeout(() => {
          // 返回上一页并刷新
          const pages = getCurrentPages();
          const prevPage = pages[pages.length - 2];
          if (prevPage) {
            prevPage.onShow(); // 触发上一页的刷新
          }
          wx.navigateBack();
        }, 1500);
      },
      fail: (err) => {
        wx.hideLoading();
        console.error('微信支付失败', err);

        // 区分用户取消和真正的支付错误
        if (err.errMsg && err.errMsg.includes('cancel')) {
          wx.showToast({
            title: '支付已取消',
            icon: 'none',
            duration: 1500
          });
        } else {
          wx.showToast({
            title: '支付失败，请重试',
            icon: 'none',
            duration: 2000
          });
        }
      },
             complete: () => {
         // 确保loading被隐藏
         wx.hideLoading();
       }
     });
   },

   // 恢复到原始订单状态
   restoreOriginalOrder() {
     if (!this.data.orderCartItems || this.data.orderCartItems.length === 0) {
       // 如果没有原始订单数据，直接返回上一页
       this.navigateBackAndRefresh();
       return;
     }

     // 检查是否有变更
     const hasChanges = this.hasOrderChanges();
     if (!hasChanges) {
       // 如果没有变更，直接返回上一页
       this.navigateBackAndRefresh();
       return;
     }

     wx.showLoading({
       title: '恢复原始订单...',
     });

     // 准备原始订单数据
     const originalOrderData = this.prepareOriginalOrderData();

     // 调用更新订单接口，恢复到原始状态
     loginRequest.post({
       url: '/biz-order/update',
       data: {
         order_id: this.data.orderId,
         ...originalOrderData
       },
       header: {
         'token': wx.getStorageSync('token')
       }
     }).then(res => {
       wx.hideLoading();

       if (res.status === 200) {
         console.log('订单已恢复到原始状态');

         // 恢复购物车状态
         this.restoreCartToOriginalState();

         // 返回上一页并刷新
         this.navigateBackAndRefresh();
       } else {
         wx.showToast({
           title: res.message || '恢复订单失败',
           icon: 'none'
         });

         // 即使恢复失败，也返回上一页
         this.navigateBackAndRefresh();
       }
     }).catch(err => {
       wx.hideLoading();
       console.error('恢复订单失败', err);
       wx.showToast({
         title: '网络错误，但仍将返回',
         icon: 'none'
       });

       // 即使出错，也返回上一页
       this.navigateBackAndRefresh();
     });
   },

   // 检查是否有订单变更
   hasOrderChanges() {
     const currentItems = this.data.cartItems || [];
     const originalItems = this.data.orderCartItems || [];

     // 如果数量不同，说明有变更
     if (currentItems.length !== originalItems.length) {
       return true;
     }

     // 检查每个商品的数量是否有变化
     for (let currentItem of currentItems) {
       const originalItem = originalItems.find(item => String(item.id) === String(currentItem.id));
       if (!originalItem || originalItem.count !== currentItem.count) {
         return true;
       }
     }

     // 检查是否有原订单中的商品被删除
     for (let originalItem of originalItems) {
       const currentItem = currentItems.find(item => String(item.id) === String(originalItem.id));
       if (!currentItem) {
         return true;
       }
     }

     return false;
   },

   // 准备原始订单数据
   prepareOriginalOrderData() {
     const originalItems = (this.data.orderCartItems || []).map(item => ({
       dish_id: item.id,
       name: item.name,
       price: item.price,
       quantity: item.count
     }));

     // 计算原始订单总金额
     const originalTotal = originalItems.reduce((total, item) => {
       return total + (item.price * item.quantity);
     }, 0);

     // 计算从当前状态恢复到原始状态需要的变更
     const changedItems = this.calculateRestoreChanges();

     return {
       store_id: this.data.storeInfo.id,
       contact_phone: this.data.contactPhone,
       contact_name: this.data.contactName,
       booking_date: this.data.bookingDate,
       booking_time: this.data.bookingTime,
       people_count: this.data.peopleCount,
       remark: this.data.orderRemark,
       total_amount: parseFloat(originalTotal.toFixed(2)),
       items: originalItems,
       changed_items: changedItems // 恢复时需要的变更项目
     };
   },

   // 计算恢复到原始状态需要的变更
   calculateRestoreChanges() {
     const currentItems = this.data.cartItems || [];
     const originalItems = this.data.orderCartItems || [];
     const changedItems = [];

     console.log('计算恢复变更 - 当前商品:', JSON.stringify(currentItems));
     console.log('计算恢复变更 - 原始商品:', JSON.stringify(originalItems));

     // 遍历当前购物车中的商品，与原始订单比较
     currentItems.forEach(currentItem => {
       const originalItem = originalItems.find(item => String(item.id) === String(currentItem.id));

       if (!originalItem) {
         // 当前商品在原始订单中不存在，需要删除
         changedItems.push({
           dish_id: currentItem.id,
           name: currentItem.name,
           price: currentItem.price,
           quantity: currentItem.count,
           change_type: 'delete'
         });
         console.log(`需要删除商品: ${currentItem.name}, 数量: ${currentItem.count}`);
       } else if (Number(currentItem.count) !== Number(originalItem.count)) {
         // 数量不同，需要更新到原始数量
         changedItems.push({
           dish_id: currentItem.id,
           name: currentItem.name,
           price: currentItem.price,
           quantity: originalItem.count,
           change_type: 'update',
           original_quantity: currentItem.count
         });
         console.log(`需要更新商品: ${currentItem.name}, 从 ${currentItem.count} 恢复到 ${originalItem.count}`);
       }
     });

     // 遍历原始订单中的商品，检查是否有被删除的商品需要恢复
     originalItems.forEach(originalItem => {
       const currentItem = currentItems.find(item => String(item.id) === String(originalItem.id));

       if (!currentItem) {
         // 原始商品在当前购物车中不存在，需要恢复（添加）
         changedItems.push({
           dish_id: originalItem.id,
           name: originalItem.name,
           price: originalItem.price,
           quantity: originalItem.count,
           change_type: 'add'
         });
         console.log(`需要恢复商品: ${originalItem.name}, 数量: ${originalItem.count}`);
       }
     });

     console.log('计算出的恢复变更:', JSON.stringify(changedItems));
     return changedItems;
   },

   // 恢复购物车到原始状态
   restoreCartToOriginalState() {
     const originalItems = [...(this.data.orderCartItems || [])];

     // 计算原始订单总计
     let totalCount = 0;
     let totalPrice = 0;

     originalItems.forEach(item => {
       totalCount += item.count;
       totalPrice += item.price * item.count;
     });

     this.setData({
       cartItems: originalItems,
       cartTotal: {
         count: totalCount,
         price: parseFloat(totalPrice.toFixed(2))
       },
       newCartItems: [],
       newCartTotal: {
         count: 0,
         price: 0
       }
     });

     // 更新菜品列表中的数量显示
     this.updateDishCountsAfterRestore();

     // 同步到菜单列表
     this.syncNewCountToMenu();
   },

   // 恢复后更新菜品列表中的数量显示
   updateDishCountsAfterRestore() {
     const originalItems = this.data.orderCartItems || [];

     // 更新dishes数组中的菜品数量
     const dishes = this.data.dishes.map(dish => {
       const originalItem = originalItems.find(item => String(item.id) === String(dish.id));
       return {
         ...dish,
         count: originalItem ? originalItem.count : 0,
         newCount: 0 // 恢复后没有新增商品
       };
     });

     // 更新当前分类下的菜品数量
     const currentCategoryDishes = this.data.currentCategoryDishes.map(dish => {
       const originalItem = originalItems.find(item => String(item.id) === String(dish.id));
       return {
         ...dish,
         count: originalItem ? originalItem.count : 0,
         newCount: 0 // 恢复后没有新增商品
       };
     });

     // 更新allDishes数组中的菜品数量
     const allDishes = this.data.allDishes ? this.data.allDishes.map(dish => {
       const originalItem = originalItems.find(item => String(item.id) === String(dish.id));
       return {
         ...dish,
         count: originalItem ? originalItem.count : 0,
         newCount: 0 // 恢复后没有新增商品
       };
     }) : [];

     this.setData({
       dishes: dishes,
       currentCategoryDishes: currentCategoryDishes,
       allDishes: allDishes.length > 0 ? allDishes : this.data.allDishes
     });
   },

   // 返回上一页并刷新
   navigateBackAndRefresh() {
     setTimeout(() => {
       // 返回上一页并刷新
       const pages = getCurrentPages();
       const prevPage = pages[pages.length - 2];
       if (prevPage) {
         prevPage.onShow(); // 触发上一页的刷新
       }
       wx.navigateBack();
     }, 100);
   }
 })