/* pages/booking_business_edit/booking_business_edit.wxss */

/* 总体主盒子 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  color: #939393;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #fff;
  color: #939393;
}

.loading {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid #ddd;
  border-top: 4rpx solid #ff4a4a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 商家信息卡片 */
.store-info {
  position: relative;
  margin-bottom: 20rpx;
}

.store-banner {
  position: relative;
  width: 100%;
  height: 140rpx;
  overflow: hidden;
}

.store-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.2)
  );
}

.store-card {
  position: relative;
  margin: -60rpx 30rpx 0;
  padding: 80rpx 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.store-avatar-container {
  position: absolute;
  top: -60rpx;
  left: 30rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  padding: 4rpx;
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.store-avatar {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}

.store-details {
  padding-top: 8rpx;
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.store-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.store-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.store-tag {
  font-size: 22rpx;
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.store-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #fff5f5;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.rating-score {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff4a4a;
}

.rating-count {
  font-size: 22rpx;
  color: #ff4a4a;
  opacity: 0.8;
}

.store-address-container {
  display: flex;
  align-items: flex-start;
  gap: 8rpx;
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
}

.address-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml;base64,PHN2ZyB0PSIxNjgxMjM0NTY3ODkwIiBjbGFzcz0iaWNvbiIgdmlld0JveD0iMCAwIDEwMjQgMTAyNCIgdmVyc2lvbj0iMS4xIiB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHAtaWQ9IjQxNzMiIHdpZHRoPSIyMDAiIGhlaWdodD0iMjAwIj48cGF0aCBkPSJNNTU0LjY2NjY2NyA4NTMuMzMzMzMzYzE3MC42NjY2NjctMTI4IDE3MC42NjY2NjYtMzQxLjMzMzMzMyAxNzAuNjY2NjY2LTM0MS4zMzMzMzMgMTcwLjY2NjY2N3MwIDE3MC42NjY2NjcgMTcwLjY2NjY2NiAzNDEuMzMzMzMzYzE3MC42NjY2NjcgMTI4IDM0MS4zMzMzMzMgMTI4IDM0MS4zMzMzMzQgMHogbTAtNjgyLjY2NjY2NmMxMjggMCAyNTYgMTI4IDI1NiAyNTZzLTEyOCAyNTYtMjU2IDI1Ni0yNTYtMTI4LTI1Ni0yNTYgMTI4LTI1NiAyNTYtMjU2eiIgZmlsbD0iIzk5OTk5OSIgcC1pZD0iNDE3NCI+PC9wYXRoPjwvc3ZnPg==")
    no-repeat center;
  background-size: contain;
  flex-shrink: 0;
}

.store-address {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 商家电话容器样式 */
.store-phone-container {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
}

/* 电话图标 */
.phone-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml;base64,************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************")
    no-repeat center;
  background-size: contain;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* 电话内容区域 */
.phone-content {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 电话号码文本样式 */
.store-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-right: 10rpx;
}

/* 拨打按钮样式 */
.call-button {
  font-size: 24rpx;
  color: #ff4d4f;
  background: #fff;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  border: 1px solid #ff4d4f;
  transition: all 0.3s ease;
  cursor: pointer;
  flex-shrink: 0;
  min-width: 80rpx;
  text-align: center;
  box-shadow: 0 2rpx 4rpx rgba(255, 77, 79, 0.1);
}

.call-button:active {
  background: #ff4d4f;
  color: #fff;
  transform: scale(0.95);
  box-shadow: 0 1rpx 2rpx rgba(255, 77, 79, 0.2);
}

.call-button:hover {
  background: rgba(255, 77, 79, 0.05);
  border-color: #ff7875;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  height: calc(100vh - 340rpx);
  overflow: hidden;
}

/* 左侧分类栏主盒子 */
.left-categories {
  display: inline-block;
  width: 200rpx;
  height: 100%;
  background: #f5f5f5;
}

.category-list {
  height: 100%;
  padding: 20rpx 0;
}

/* 左侧分类栏list的item */
.category-item {
  height: 40px;
  line-height: 40px;
  padding: 5px 35rpx;
  font-size: 26rpx;
  color: #333;
}

/* 左侧分类栏list的item被选中时 */
.category-item.active {
  background: #fff;
  color: #ff4a4a;
}

.category-name {
  font-size: 26rpx;
  text-align: center;
}

/* 右侧商品列表主盒子 */
.right-products {
  display: inline-block;
  height: 100%;
  width: 550rpx;
  padding: 14rpx;
  box-sizing: border-box;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
}

.product-list {
  height: 100%;
  padding-bottom: 300rpx;
}

.product-category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 20rpx 0 16rpx 0;
  margin-bottom: 8rpx;
}

/* 商品卡片 - 采用classify的横向布局 */
.product-item {
  width: 100%;
  min-height: 200rpx;
  background-color: #fff;
  margin-bottom: 14rpx;
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  box-sizing: border-box;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  object-fit: cover;
}

.product-info {
  flex: 1;
  margin-left: 15rpx;
  padding: 10rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
  border-bottom: 1px solid #ddd;
}

.product-name {
  color: #000;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.product-desc {
  font-size: 22rpx;
  color: #939393;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  margin-bottom: 8rpx;
  flex-shrink: 0;
}

.product-sales {
  font-size: 22rpx;
  color: #939393;
  display: block;
}

.product-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  flex-shrink: 0;
}

.product-price {
  color: #ff4a4a;
  font-size: 28rpx;
  font-weight: 700;
  flex: 1;
}

/* 商品操作按钮 - 简化样式 */
.product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
}

.action-minus,
.action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  cursor: pointer;
}

.action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

/* 空商品提示 */
.empty-products {
  padding: 100rpx 32rpx;
  text-align: center;
}

.empty-text {
  color: #939393;
  font-size: 24rpx;
  text-align: center;
}

/* 底部空白区域 */
.bottom-space {
  height: 200rpx;
  width: 100%;
}

/* 购物车 */
.cart-container {
  position: fixed;
  bottom: 40rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  z-index: 100;
  border: 1px solid #ddd;
}

.cart-expanded {
  z-index: 160;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 16rpx 16rpx 0 0;
}

.cart-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 150;
}

.cart-header {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding: 0 30rpx;
  justify-content: space-between;
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  z-index: 170;
}

.cart-expanded .cart-header {
  border-radius: 16rpx 16rpx 0 0;
}

.cart-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.cart-icon-container {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
}

.cart-icon.active {
  background: #ff4a4a;
  border-color: #ff4a4a;
}

.cart-icon-text {
  font-size: 28rpx;
  color: #939393;
}

.cart-icon.active .cart-icon-text {
  color: #fff;
}

.cart-count {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4757;
  color: #fff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  font-weight: 600;
}

.cart-info {
  flex: 1;
}

.cart-price {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.cart-empty {
  font-size: 24rpx;
  color: #939393;
}

.cart-submit {
  width: 180rpx;
  height: 68rpx;
  border-radius: 34rpx;
  background: #f5f5f5;
  color: #939393;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #ddd;
}

.cart-submit.active {
  background: #ff4a4a;
  color: #fff;
  border-color: #ff4a4a;
}

.cart-content {
  height: 400rpx;
  background: #fff;
  border-top: 1px solid #ddd;
  position: relative;
  z-index: 170;
}

.cart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #ddd;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  background: #fff;
}

.cart-clear {
  color: #939393;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
  border: 1px solid #ddd;
}

.cart-items {
  height: 320rpx;
  padding: 0 16rpx;
  background: #fff;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 18rpx 0;
  border-bottom: 1px solid #ddd;
  position: relative;
  min-height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  max-width: 445rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
  flex-shrink: 1;
}

.cart-item-price-action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 240rpx;
  justify-content: space-between;
  position: relative;
}

.cart-item-price {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 600;
  width: 80rpx;
  text-align: right;
  flex-shrink: 0;
}

/* 购物车中product-action的样式 */
.cart-item .product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
  width: 140rpx;
  flex-shrink: 0;
  justify-content: space-between;
}

.cart-item .action-minus,
.cart-item .action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  cursor: pointer;
  flex-shrink: 0;
}

.cart-item .action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cart-item .action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.cart-item .action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

/* 弹窗样式 */
.dish-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
}

.modal-mask {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  z-index: 10;
}

.modal-image {
  width: 100%;
  height: 360rpx;
  object-fit: cover;
}

.modal-info {
  padding: 28rpx;
  background: #fff;
}

.modal-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.modal-desc {
  font-size: 26rpx;
  color: #939393;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.modal-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #ddd;
}

.modal-price {
  font-size: 36rpx;
  color: #ff4a4a;
  font-weight: 600;
}

/* 结算弹窗优化 */
.checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.5);
}

.checkout-result-content {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  max-width: 600rpx;
  background: #fff;
  border-radius: 16rpx;
  z-index: 1001;
  padding: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.checkout-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1px solid #eee;
}

.checkout-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.checkout-result-info {
  margin: 30rpx 0;
  padding: 20rpx;
  background: #f8f8f8;
  border-radius: 8rpx;
}

.result-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.result-label {
  color: #666;
}

.result-value {
  color: #333;
  font-weight: 500;
}

.checkout-options {
  margin-top: 40rpx;
  text-align: center;
}

.checkout-question {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 30rpx;
  display: block;
}

.checkout-actions {
  display: flex;
  justify-content: space-between;
  margin-top: 30rpx;
}

/* 完全重写按钮样式，解决边框虚线问题 */
.checkout-btn {
  flex: 1;
  margin: 0 10rpx;
  height: 80rpx;
  line-height: 80rpx;
  font-size: 28rpx;
  border-radius: 40rpx;
  background-color: #fff;
  border: 1rpx solid #ddd;
  text-align: center;
  position: relative;
  padding: 0;
  box-sizing: border-box;
}

/* 去除微信小程序按钮的默认样式 */
.checkout-btn::after {
  display: none;
  border: none;
}

.checkout-btn.primary {
  background-color: #07c160;
  color: white;
  border-color: #07c160;
}

/* 修复点击态样式 */
.checkout-btn:active {
  opacity: 0.8;
}

/* 二维码容器样式 */
.qrcode-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  margin-top: 30rpx;
}

.qrcode-title {
  font-size: 32rpx;
  color: #333;
  margin-bottom: 20rpx;
}

.qrcode-tip {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
}

.qrcode-url {
  font-size: 24rpx;
  color: #07c160;
  margin-top: 10rpx;
  word-break: break-all;
  text-align: center;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .checkout-content {
    width: 90%;
  }

  .success-actions {
    flex-direction: column;
    gap: 12rpx;
  }

  .success-btn {
    width: 240rpx;
  }
}

/* 修改关闭按钮位置 */
.checkout-content .modal-close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-container {
  background: #fff;
  border-radius: 12px;
  width: 80%;
  padding: 20px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.payment-amount {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin: 30rpx 0;
  color: #ff4a4a;
  text-shadow: 0 2rpx 4rpx rgba(255, 74, 74, 0.1);
}

.payment-methods {
  margin: 30rpx 0;
}

.payment-methods text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 支付方式标题容器 */
.payment-methods-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 红色提示文字样式 */
.payment-notice-red {
  font-size: 26rpx !important;
  color: #ff4a4a !important;
  font-weight: 400 !important;
  margin-bottom: 0 !important;
  display: inline !important;
}

/* 支付方式选项样式优化 */
.payment-method {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 20rpx;
  position: relative;
  justify-content: space-between;
}

/* 统一支付图标样式 */
.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 支付方式左侧容器 */
.payment-method-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

/* 支付方式文字 */
.payment-method-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 余额信息容器 */
.balance-info {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.balance-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.balance-amount {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 500;
}

/* 单选按钮容器 */
.radio-container {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.payment-method.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.payment-method.selected .radio-btn {
  background: #07c160;
  border-color: #07c160;
}

.payment-method.selected .radio-btn::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 余额不足时的禁用状态 */
.payment-method.disabled {
  opacity: 0.5;
  background: #f5f5f5;
}

/* 余额不足提示 */
.insufficient-tip {
  font-size: 24rpx;
  color: #ff4a4a;
  position: absolute;
  right: 80rpx;
  top: 50%;
  transform: translateY(-50%);
}

/* 确认支付按钮样式 */
.confirm-payment-btn {
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 24rpx;
  width: 100%;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 30rpx;
  transition: all 0.3s ease;
}

.confirm-payment-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 禁用状态的支付方式 */
.payment-method.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.payment-method.disabled .radio-btn {
  border-color: #ddd;
  background: #f5f5f5;
}

.deposit-tip {
  padding: 10px 20px;
  margin: 10px 0;
  font-size: 14px;
  color: #ff6b00;
  line-height: 1.5;
  text-align: left;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.enterprise-list {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.enterprise-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.enterprise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  background: #fff;
}

.enterprise-item.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.enterprise-name {
  font-size: 28rpx;
  color: #333;
}

.enterprise-item .radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.enterprise-item.selected .radio-btn {
  border-color: #07c160;
  background: #07c160;
}

.enterprise-item.selected .radio-btn::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.no-enterprise {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

/* 左下角查看预订信息按钮 */
.user-info-btn {
  position: fixed;
  left: 30rpx;
  bottom: 200rpx;
  background-color: #07c160;
  color: #fff;
  padding: 20rpx 30rpx;
  border-radius: 50rpx;
  box-shadow: 0 4rpx 12rpx rgba(37, 189, 115, 0.3);
  z-index: 100;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 用户信息弹窗 */
.user-info-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.user-info-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  max-height: 80vh;
  display: flex;
  flex-direction: column;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.user-info-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.user-info-title {
  font-size: 34rpx;
  font-weight: 600;
  color: #333;
}

.modal-close {
  font-size: 40rpx;
  color: #999;
  line-height: 1;
}

.user-info-scroll {
  flex: 1;
  max-height: 60vh;
  padding: 20rpx 30rpx;
}

.user-info-section {
  margin-bottom: 30rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

.user-info-item {
  display: flex;
  margin-bottom: 20rpx;
  align-items: flex-start;
}

.info-label {
  width: 180rpx;
  font-size: 28rpx;
  color: #666;
  flex-shrink: 0;
}

.info-value {
  flex: 1;
  font-size: 28rpx;
  color: #333;
  word-break: break-all;
}

.user-info-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.close-btn {
  background-color: #07c160;
  color: #fff;
  border-radius: 50rpx;
  font-size: 30rpx;
  padding: 16rpx 0;
  width: 100%;
  text-align: center;
  border: none;
}

/* 地址文本样式 - 修改字体大小 */
.store-address {
  font-size: 26rpx; /* 原来是26rpx，改小了 */
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 原订单信息样式 */
.original-order-info {
  padding: 10px;
  background-color: #f8f8f8;
  border-radius: 4px;
  margin-bottom: 10px;
}

.original-order-title {
  font-size: 14px;
  color: #666;
}

/* 购物车汇总样式 */
.cart-summary {
  border-top: 1px solid #eee;
  padding: 10px;
  background-color: #f9f9f9;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 14px;
}

.summary-total {
  display: flex;
  justify-content: space-between;
  font-weight: bold;
  font-size: 16px;
  color: #ff6b35;
  border-top: 1px solid #ddd;
  padding-top: 5px;
  margin-top: 5px;
}

.empty-new-items {
  padding: 20px;
  text-align: center;
}

.empty-text {
  color: #999;
  font-size: 14px;
}

/* 温馨提示样式 */
.tips-section {
  padding: 15rpx 30rpx;
  background-color: #fff2f0;
  border-left: 4rpx solid #ff4d4f;
  margin: 0 30rpx 20rpx 30rpx;
  border-radius: 8rpx;
}

.tips-content {
  line-height: 1.6;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.tips-text {
  color: #ff4d4f;
  font-size: 24rpx;
  display: block;
  flex: 1;
  margin-right: 20rpx;
}

/* 商品列表区域样式 - 与其他信息区域保持一致 */
.order-items-section {
  margin-bottom: 30rpx; /* 与 user-info-section 保持一致 */
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  display: block;
}

/* 订单商品项 - 采用两行布局 */
.order-item {
  padding: 12rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item:last-child {
  border-bottom: none;
}

/* 第一行：图片 + 商品名 */
.item-row-1 {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.item-image {
  width: 40rpx;
  height: 40rpx;
  border-radius: 4rpx;
  margin-right: 10rpx;
  object-fit: cover;
  flex-shrink: 0;
}

.item-name {
  flex: 1;
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* 第二行：价格 + 数量 + 小计 */
.item-row-2 {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-left: 50rpx; /* 与图片宽度 + 间距对齐 */
}

.item-price {
  font-size: 22rpx;
  color: #ff4a4a;
  font-weight: 500;
}

.item-quantity {
  font-size: 22rpx;
  color: #666;
}

.item-subtotal {
  font-size: 22rpx;
  color: #333;
  font-weight: 600;
}

/* 备选方案：垂直布局 */
.order-item-vertical {
  display: flex;
  flex-direction: column;
  padding: 12rpx 0;
  border-bottom: 1px solid #f0f0f0;
}

.order-item-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.order-item-row:last-child {
  margin-bottom: 0;
}

/* 订单合计金额样式 */
.order-total {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15rpx 0;
  margin-top: 10rpx;
  border-top: 1px solid #eee;
  background-color: #f9f9f9;
}

.total-label {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.total-amount {
  font-size: 28rpx;
  font-weight: 600;
  color: #ff4a4a;
}

.order-total {
  margin-top: 15rpx;
  padding-top: 15rpx;
  border-top: 1rpx solid #eee;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
}

.total-label {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.total-amount {
  font-size: 36rpx;
  font-weight: bold;
  color: #ff6b35;
}

/* 优惠券选择区域 */
.coupon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx 16rpx;
}

.coupon-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.coupon-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.coupon-text {
  color: #999;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.coupon-text.selected {
  color: #4080ff;
}

.coupon-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.coupon-discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx 0;
  margin-bottom: 20rpx;
}

.discount-label {
  color: #666;
  font-size: 26rpx;
}

.discount-amount {
  color: #ff4444;
  font-size: 26rpx;
  font-weight: 500;
}

.clear-all-coupons {
  padding: 16rpx 20rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  margin: 0 -20rpx;
}

.clear-all-text {
  color: #ff6b6b;
  font-size: 26rpx;
}

/* 已选择的优惠券详情 */
.selected-coupons {
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
  margin: 0 -20rpx;
  padding: 0 20rpx;
}

.coupon-item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item-detail:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.coupon-name {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.coupon-type {
  color: #666;
  font-size: 22rpx;
}

.coupon-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.coupon-discount-amount {
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 600;
}

.cancel-coupon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 50%;
  border: 1rpx solid #ddd;
}

.cancel-icon {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}
