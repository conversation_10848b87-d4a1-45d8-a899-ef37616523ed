import { checkLogin } from '../../service/user';
import { getPublishedArticles } from '../../service/article';

Page({
  data: {
    title: '乙禾素',
    articles: [],
    // 新增轮播广告数据
    swiperArticles: [],
    displayArticles: [],
    loading: true,
    error: null,
    // 轮播相关配置
    swiperCurrent: 0,
    autoplay: true,
    interval: 3000,
    duration: 500
  },
  onLoad: function (options) {
    wx.setNavigationBarTitle({
        title: '乙禾素'
    });
    this.loadArticles();
  },

  /**
   * 加载文章列表
   */
  async loadArticles() {
    try {
      this.setData({ loading: true, error: null });

      const response = await getPublishedArticles();

      if (response.status === 200 && response.data) {
        // 按order字段分组处理文章
        const allArticles = response.data;

        // 过滤并排序轮播文章（sort_order <= 10，如果没有sort_order字段则默认为999）
        const swiperArticles = allArticles
          .filter(article => (article.sort_order || 999) <= 10)
          .sort((a, b) => (a.sort_order || 999) - (b.sort_order || 999));
        console.log("swiperArticles:", swiperArticles);
        // 过滤展示文章（sort_order > 10，如果没有sort_order字段则默认为999）
        const displayArticles = allArticles
          .filter(article => (article.sort_order || 999) > 10);
        console.log("displayArticles:", displayArticles);

        this.setData({
          articles: allArticles,
          swiperArticles: swiperArticles,
          displayArticles: displayArticles,
          loading: false
        });
      } else {
        this.setData({
          error: response.message || '获取文章列表失败',
          loading: false
        });
      }
    } catch (error) {
      console.error('获取文章列表失败:', error);
      this.setData({
        error: '网络请求失败，请稍后重试',
        loading: false
      });
    }
  },

  /**
   * 轮播切换事件
   */
  onSwiperChange(e) {
    this.setData({
      swiperCurrent: e.detail.current
    });
  },

  /**
   * 点击轮播广告跳转到详情页或广告链接
   */
  onSwiperClick(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      // 查找对应的文章数据
      const article = this.data.swiperArticles.find(item => item.id == articleId);
      
      // 如果文章包含广告链接，则跳转到广告链接
      if (article && article.ad_link) {
        this.handleAdLinkNavigation(article.ad_link);
      } else {
        // 否则跳转到文章详情页
        wx.navigateTo({
          url: `/pages/index/article/article?id=${articleId}`
        });
      }
    }
  },

  /**
   * 点击文章跳转到详情页或广告链接
   */
  onArticleClick(e) {
    const articleId = e.currentTarget.dataset.id;
    if (articleId) {
      // 查找对应的文章数据
      const article = this.data.displayArticles.find(item => item.id == articleId);
      
      // 如果文章包含广告链接，则跳转到广告链接
      if (article && article.ad_link) {
        this.handleAdLinkNavigation(article.ad_link);
      } else {
        // 否则跳转到文章详情页
        wx.navigateTo({
          url: `/pages/index/article/article?id=${articleId}`
        });
      }
    }
  },

/**
   * 处理广告链接跳转
   */
handleAdLinkNavigation(adLink) {
  try {
    // 检查链接格式
    if (!adLink || typeof adLink !== 'string') {
      console.warn('广告链接格式无效:', adLink);
      return;
    }

    // 判断链接类型并进行相应跳转
    if (adLink.startsWith('http://') || adLink.startsWith('https://')) {
      // 外部链接，使用web-view页面打开
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(adLink)}`,
        fail: (err) => {
          console.error('跳转外部链接失败:', err);
          // 如果没有webview页面，尝试复制链接到剪贴板
          wx.setClipboardData({
            data: adLink,
            success: () => {
              wx.showToast({
                title: '链接已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      });
    } else if (adLink.startsWith('/pages/')) {
      // 小程序内部页面跳转
      wx.navigateTo({
        url: adLink,
        fail: (err) => {
          console.error('跳转内部页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else if (adLink.includes('type=') && (adLink.includes('personal') || adLink.includes('employee'))) {
      // 临时点餐链接
      const typeMatch = adLink.match(/type=([^&]*)/);
      const timeoutMatch = adLink.match(/timeout=([^&]*)/);
      
      let url = '/pages/interactive_ordering/interactive_ordering';
      const params = [];
      
      if (typeMatch && typeMatch[1]) {
        params.push(`type=${typeMatch[1]}`);
      }
      if (timeoutMatch && timeoutMatch[1]) {
        params.push(`timeout=${timeoutMatch[1]}`);
      }
      
      if (params.length > 0) {
        url += '?' + params.join('&');
      }
      
      wx.navigateTo({
        url: url,
        fail: (err) => {
          console.error('跳转临时点餐页面失败:', err);
          wx.showToast({
            title: '页面跳转失败',
            icon: 'none'
          });
        }
      });
    } else {
      // 其他格式，尝试作为小程序页面路径处理
      wx.navigateTo({
        url: adLink,
        fail: (err) => {
          console.error('跳转页面失败:', err);
          // 如果跳转失败，复制链接到剪贴板
          wx.setClipboardData({
            data: adLink,
            success: () => {
              wx.showToast({
                title: '链接已复制到剪贴板',
                icon: 'success'
              });
            }
          });
        }
      });
    }
  } catch (error) {
    console.error('处理广告链接失败:', error);
    wx.showToast({
      title: '链接处理失败',
      icon: 'none'
    });
  }
},

  /**
   * 重试加载文章
   */
  onRetry() {
    this.loadArticles();
  },
    /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },
  // 在各主要页面的 onShow 方法中添加
  async onShow() {
    try {
      const isLoggedIn = await checkLogin()
      if (!isLoggedIn) {
        // token 无效，重新登录
        const app = getApp()
        if (app && app.monitor_token) {
          await app.monitor_token()
          // 重新登录后重新加载文章
          this.loadArticles()
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          })
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error)
    }
  },
})