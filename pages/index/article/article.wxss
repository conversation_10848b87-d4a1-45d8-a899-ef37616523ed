/* 页面容器 */
page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* 加载状态 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 50vh;
}

.loading-text {
  color: #999;
  font-size: 28rpx;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  height: 50vh;
  padding: 40rpx;
}

.error-text {
  color: #f56c6c;
  font-size: 28rpx;
  margin-bottom: 40rpx;
  text-align: center;
}

.retry-btn {
  background-color: #67c23a;
  color: white;
  border: none;
  border-radius: 8rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 文章容器 */
.article-container {
  background-color: white;
  margin: 20rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.1);
  margin-bottom: 160rpx; /* 为底部操作栏留出更多空间 */
}

/* 文章头部 */
.article-header {
  padding: 40rpx;
  border-bottom: 1rpx solid #eee;
}

.article-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333;
  line-height: 1.4;
  margin-bottom: 20rpx;
}

.article-summary {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
  margin-bottom: 20rpx;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 8rpx;
  border-left: 4rpx solid #67c23a;
}

.article-meta {
  display: flex;
  align-items: center;
  color: #999;
  font-size: 24rpx;
}

.meta-time {
  margin-right: 20rpx;
}

/* 文章封面图 */
.article-image-container {
  width: 100%;
  position: relative;
}

.article-image {
  width: 100%;
  display: block;
  cursor: pointer;
  transition: transform 0.2s ease;
}

.article-image:active {
  transform: scale(0.98);
}

.article-image-container::after {
  content: "点击放大";
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  background-color: rgba(0, 0, 0, 0.6);
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 22rpx;
  opacity: 0.8;
  pointer-events: none;
}

/* 文章内容 */
.article-content {
  padding: 40rpx;
}

.content-text {
  font-size: 30rpx;
  color: #333;
  line-height: 1.8;
  white-space: pre-wrap;
  word-wrap: break-word;
}

/* 分享操作区 */
.share-section {
  padding: 30rpx 40rpx;
  border-top: 1rpx solid #eee;
  background-color: #fafafa;
}

.share-action-btn {
  width: 100%;
  height: 80rpx;
  background: linear-gradient(135deg, #67c23a, #85ce61);
  color: white;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  box-shadow: 0 4rpx 12rpx rgba(103, 194, 58, 0.3);
  transition: all 0.2s ease;
}

.share-action-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 8rpx rgba(103, 194, 58, 0.3);
}

.share-action-btn::after {
  border: none;
}

.share-icon {
  margin-right: 10rpx;
  font-size: 32rpx;
}

/* 标签 */
.article-tags {
  padding: 40rpx;
  border-top: 1rpx solid #eee;
}

.tags-title {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 20rpx;
}

.tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.tag-item {
  background-color: #67c23a;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

/* 底部操作栏 */
.article-footer {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: white;
  padding: 20rpx;
  display: flex;
  gap: 20rpx;
  border-top: 1rpx solid #eee;
  z-index: 100;
}

.footer-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  display: flex;
  justify-content: center;
  align-items: center;
}

.back-btn {
  background-color: #e4e7ed;
  color: #606266;
  border: none;
}

.share-btn {
  background-color: #67c23a;
  color: white;
  border: none;
}

.footer-btn::after {
  border: none;
}

/* 响应式设计 */
@media (max-width: 480px) {
  .article-title {
    font-size: 32rpx;
  }

  .content-text {
    font-size: 28rpx;
  }

  .article-container {
    margin: 10rpx;
  }
}
