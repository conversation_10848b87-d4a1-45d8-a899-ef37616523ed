import { getArticleDetail } from '../../../service/article';
import { checkLogin } from '../../../service/user';

Page({
  data: {
    articleId: null,
    article: null,
    loading: true,
    error: null,
    sharerId: null,  // 分享人ID
    currentUserId: null  // 当前用户ID
  },

  onLoad: function (options) {
    const articleId = options.id || options.articleId;
    const sharerId = options.sharerId || null;  // 获取分享人ID
    console.log('onLoad sharerId:', sharerId);
    console.log('onLoad articleId:', articleId);
    if (articleId) {
      this.setData({
        articleId: parseInt(articleId),
        sharerId: sharerId ? parseInt(sharerId) : null
      });
      console.log('onLoad this.data:', this.data);
      // 先获取当前用户ID，然后再获取文章详情
      this.getCurrentUserId().then(() => {
        this.getArticleDetail(articleId);
      });
    } else {
      this.setData({
        error: '文章ID不能为空',
        loading: false
      });
    }
  },

  /**
   * 获取当前用户ID
   */
  async getCurrentUserId() {
    try {
      const app = getApp();
      
      // 如果全局数据中已有用户信息，直接使用
      if (app.globalData.userInfo && app.globalData.userInfo.id) {
        this.setData({
          currentUserId: app.globalData.userInfo.id
        });
        return app.globalData.userInfo.id;
      }
      
      // 如果没有用户信息，尝试获取
      const { getUserInfo } = require('../../../service/user');
      const userInfo = await getUserInfo();
      
      if (userInfo && userInfo.id) {
        this.setData({
          currentUserId: userInfo.id
        });
        return userInfo.id;
      }
      
      console.warn('无法获取用户ID');
      return null;
    } catch (error) {
      console.error('获取当前用户ID失败:', error);
      return null;
    }
  },

  /**
   * 获取文章详情
   */
  async getArticleDetail(articleId) {
    try {
      this.setData({ loading: true, error: null });
      
      // 判断是否需要传递分享人ID
      const { sharerId, currentUserId } = this.data;
      console.log('getArticleDetail this.data:', this.data);
      console.log('getArticleDetail sharerId:', sharerId);
      console.log('getArticleDetail currentUserId:', currentUserId);
      let shareParam = null;
      
      // 如果有分享人ID且与当前用户ID不同，则传递分享人ID
      if (sharerId && currentUserId && sharerId !== currentUserId) {
        shareParam = sharerId;
      }
      shareParam = sharerId;
      console.log('getArticleDetail shareParam:', shareParam);
      console.log('getArticleDetail articleId:', articleId);
      const response = await getArticleDetail(articleId, shareParam);

      if (response.status === 200 && response.data && response.data.article) {
        this.setData({
          article: response.data.article,
          loading: false
        });
        
        // 设置页面标题
        if (response.data.article.name) {
          wx.setNavigationBarTitle({
            title: response.data.article.name
          });
        }
      } else {
        this.setData({
          error: response.message || '获取文章详情失败',
          loading: false
        });
      }
    } catch (error) {
      console.error('获取文章详情失败:', error);
      this.setData({
        error: '网络请求失败，请稍后重试',
        loading: false
      });
    }
  },

  /**
   * 重试获取文章
   */
  onRetry() {
    if (this.data.articleId) {
      this.getArticleDetail(this.data.articleId);
    }
  },

  /**
   * 返回上一页
   */
  onBack() {
    wx.navigateBack();
  },

  /**
   * 图片点击放大预览
   */
  onImageTap() {
    const article = this.data.article;
    if (article && article.image) {
      wx.previewImage({
        current: article.image,
        urls: [article.image],
        fail: (err) => {
          console.error('图片预览失败:', err);
          wx.showToast({
            title: '图片预览失败',
            icon: 'none'
          });
        }
      });
    }
  },

  /**
   * 手动触发分享
   */
  onManualShare() {
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 分享文章
   */
  onShareAppMessage() {
    const { article, articleId, currentUserId } = this.data;
    console.log('onShareAppMessage this.data:', this.data);
    // 构建分享路径，带上当前用户ID作为分享人
    let sharePath = `/pages/index/article/article?id=${articleId}`;
    if (currentUserId) {
      sharePath += `&sharerId=${currentUserId}`;
    }
    console.log('onShareAppMessage sharePath:', sharePath);
    console.log('onShareAppMessage article:', article);
    if (article) {
      return {
        title: article.name || '素食文章分享',
        desc: article.summary || '来看看这篇有趣的素食文章',
        path: sharePath,
        imageUrl: article.thumbnail || article.image || ''
      };
    }
    return {
      title: '素食文章分享',
      path: sharePath
    };
  },

  /**
   * 检查登录状态
   */
  async onShow() {
    try {
      const isLoggedIn = await checkLogin();
      if (!isLoggedIn) {
        const app = getApp();
        if (app && app.monitor_token) {
          await app.monitor_token();
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error);
    }
  }
}); 