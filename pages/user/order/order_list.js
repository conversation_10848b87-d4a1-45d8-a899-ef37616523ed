// pages/user/order/order_list.js
import { 
  getOrderList, 
  cancelOrder, 
  formatOrderStatus, 
  formatPaymentStatus, 
  formatOrderType,
  formatPaymentMethod,
  formatDateTime,
  canCancelOrder 
} from '../../../service/order';
import { checkLogin } from '../../../service/user';

const app = getApp();

Page({
  data: {
    isLogin: false,
    orders: [],
    loading: false,
    hasMore: true,
    skip: 0,
    limit: 20,
    total: 0,
    refreshing: false
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: '订单列表'
    });
    this.checkLoginAndLoadData();
  },

  async onShow() {
    try {
      const isLoggedIn = await checkLogin();
      if (!isLoggedIn) {
        const app = getApp();
        if (app && app.monitor_token) {
          await app.monitor_token();
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error);
    }
    this.checkLoginStatus();
  },

  checkLoginStatus: function() {
    const token = wx.getStorageSync('token') || '';
    
    if (token && app.globalData && app.globalData.userInfo) {
      this.setData({
        isLogin: true
      });
    } else {
      this.setData({
        isLogin: !!token
      });
    }
  },

  checkLoginAndLoadData: function() {
    this.checkLoginStatus();
    if (this.data.isLogin) {
      this.loadOrderList();
    }
  },

  // 加载订单列表
  loadOrderList: function(refresh = false) {
    if (this.data.loading) return;

    if (refresh) {
      this.setData({
        skip: 0,
        orders: [],
        hasMore: true,
        refreshing: true
      });
    }

    this.setData({ loading: true });

    const params = {
      skip: refresh ? 0 : this.data.skip,
      limit: this.data.limit
    };

    getOrderList(params)
      .then(res => {
        console.log('订单列表API响应:', res);
        
        if (res.code === 200 && res.data) {
          const newOrders = res.data.list || [];
          const total = res.data.total || 0;
          
          console.log(`获取到 ${newOrders.length} 条订单数据，总计 ${total} 条`);
          
          // 格式化订单数据
          const formattedOrders = newOrders.map(order => ({
            ...order,
            statusText: formatOrderStatus(order.status),
            paymentStatusText: formatPaymentStatus(order.payment_status),
            typeText: formatOrderType(order.type),
            paymentMethodText: formatPaymentMethod(order.payment_method),
            createdAtText: formatDateTime(order.created_at),
            updatedAtText: formatDateTime(order.updated_at),
            paymentTimeText: formatDateTime(order.payment_time),
            canCancel: canCancelOrder(order)
          }));

          const orders = refresh ? formattedOrders : [...this.data.orders, ...formattedOrders];
          
          console.log(`设置页面数据: ${orders.length} 条订单`);
          
          this.setData({
            orders: orders,
            total: total,
            skip: orders.length,
            hasMore: orders.length < total,
            loading: false,
            refreshing: false
          });

          if (orders.length === 0) {
            wx.showToast({
              title: '暂无订单记录',
              icon: 'none'
            });
          } else {
            console.log('订单数据已成功设置到页面');
          }
        } else {
          console.error('API响应格式错误:', res);
          throw new Error(res.message || '获取订单列表失败');
        }
      })
      .catch(error => {
        console.error('获取订单列表失败:', error);
        this.setData({
          loading: false,
          refreshing: false
        });
        wx.showToast({
          title: error.message || '获取订单列表失败',
          icon: 'none'
        });
      });
  },

  // 下拉刷新
  onPullDownRefresh: function() {
    this.loadOrderList(true);
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 1000);
  },

  // 上拉加载更多
  onReachBottom: function() {
    if (this.data.hasMore && !this.data.loading) {
      this.loadOrderList();
    }
  },

  // 查看订单详情
  viewOrderDetail: function(e) {
    const orderId = e.currentTarget.dataset.id;
    wx.navigateTo({
      url: `/pages/user/order/order_detail?id=${orderId}`
    });
  },

  // 取消订单
  cancelOrder: function(e) {
    const orderId = e.currentTarget.dataset.id;
    const orderNo = e.currentTarget.dataset.orderNo;
    
    wx.showModal({
      title: '确认取消',
      content: `确定要取消订单 ${orderNo} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder(orderId);
        }
      }
    });
  },

  // 执行取消订单操作
  performCancelOrder: function(orderId) {
    wx.showLoading({
      title: '取消中...'
    });

    cancelOrder(orderId)
      .then(res => {
        wx.hideLoading();
        console.log('取消订单响应:', res);
        
        if (res.code === 200) {
          wx.showToast({
            title: '订单取消成功',
            icon: 'success'
          });
          
          // 刷新订单列表
          this.loadOrderList(true);
        } else {
          throw new Error(res.message || '取消订单失败');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('取消订单失败:', error);
        wx.showToast({
          title: error.message || '取消订单失败',
          icon: 'none'
        });
      });
  },

  // 登录按钮点击事件
  handleLogin: function() {
    wx.navigateTo({
      url: '/pages/phoneAuth/phoneAuth'
    });
  },

  // 返回按钮
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
