/* pages/user/order/order_list.wxss */
@import "../../../pages/user/user.wxss";

.order-list {
  padding: 0;
}

.order-item {
  background: #fff;
  margin-bottom: 20rpx;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 24rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background: #fafafa;
}

.order-info {
  flex: 1;
}

.order-no {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.order-type {
  font-size: 24rpx;
  color: #666;
}

.order-status {
  text-align: right;
}

.status-text {
  font-size: 26rpx;
  font-weight: 600;
  margin-bottom: 4rpx;
}

.status-text.pending {
  color: #ff9500;
}

.status-text.paid {
  color: #34c759;
}

.status-text.shipped {
  color: #007aff;
}

.status-text.delivered {
  color: #30d158;
}

.status-text.completed {
  color: #34c759;
}

.status-text.cancelled {
  color: #ff3b30;
}

.status-text.refunded {
  color: #ff9500;
}

.payment-status {
  font-size: 22rpx;
  color: #999;
}

.order-content {
  padding: 24rpx;
}

.order-amount {
  margin-bottom: 20rpx;
}

.amount-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.amount-label {
  font-size: 26rpx;
  color: #666;
}

.amount-value {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
}

.amount-value.payable {
  color: #ff9500;
}

.amount-value.paid {
  color: #34c759;
}

.order-meta {
  border-top: 1rpx solid #f0f0f0;
  padding-top: 16rpx;
}

.meta-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8rpx;
}

.meta-label {
  font-size: 24rpx;
  color: #999;
}

.meta-value {
  font-size: 24rpx;
  color: #666;
}

.order-actions {
  padding: 16rpx 24rpx;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  text-align: right;
}

.cancel-btn {
  background: #ff3b30;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 12rpx 24rpx;
  font-size: 24rpx;
  line-height: 1;
}

.cancel-btn:active {
  background: #d70015;
}

/* 空状态样式 */
.empty-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.3;
}

.empty-text {
  font-size: 28rpx;
  color: #999;
}

/* 加载状态样式 */
.loading-state {
  text-align: center;
  padding: 120rpx 40rpx;
}

.loading-icon {
  font-size: 120rpx;
  margin-bottom: 24rpx;
  opacity: 0.6;
  animation: rotate 2s linear infinite;
}

.loading-text {
  font-size: 28rpx;
  color: #999;
}

@keyframes rotate {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* 加载更多和没有更多样式 */
.load-more,
.no-more {
  text-align: center;
  padding: 40rpx;
  font-size: 24rpx;
  color: #999;
}

/* 底部按钮容器 */
.bottom-button-container {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  padding: 20rpx 40rpx;
  border-top: 1rpx solid #e0e0e0;
  box-shadow: 0 -2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.back-button {
  width: 100%;
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 8rpx;
  padding: 24rpx;
  font-size: 32rpx;
  font-weight: 600;
}

.back-button:active {
  background: #0056cc;
}

/* 为底部按钮留出空间 */
.readlog-container {
  padding-bottom: 120rpx;
}
