// pages/user/order/order_detail.js
import { 
  getOrderDetail, 
  cancelOrder, 
  formatOrderStatus, 
  formatPaymentStatus, 
  formatOrderType,
  formatPaymentMethod,
  formatDateTime,
  canCancelOrder 
} from '../../../service/order';
import { checkLogin } from '../../../service/user';

const app = getApp();

Page({
  data: {
    isLogin: false,
    order: null,
    loading: true,
    orderId: null
  },

  onLoad: function(options) {
    wx.setNavigationBarTitle({
      title: '订单详情'
    });
    
    if (options.id) {
      this.setData({
        orderId: options.id
      });
      this.checkLoginAndLoadData();
    } else {
      wx.showToast({
        title: '订单ID不能为空',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  async onShow() {
    try {
      const isLoggedIn = await checkLogin();
      if (!isLoggedIn) {
        const app = getApp();
        if (app && app.monitor_token) {
          await app.monitor_token();
        } else {
          wx.navigateTo({
            url: '/pages/index/index'
          });
        }
      }
    } catch (error) {
      console.error('检查登录状态失败', error);
    }
    this.checkLoginStatus();
  },

  checkLoginStatus: function() {
    const token = wx.getStorageSync('token') || '';
    
    if (token && app.globalData && app.globalData.userInfo) {
      this.setData({
        isLogin: true
      });
    } else {
      this.setData({
        isLogin: !!token
      });
    }
  },

  checkLoginAndLoadData: function() {
    this.checkLoginStatus();
    if (this.data.isLogin && this.data.orderId) {
      this.loadOrderDetail();
    }
  },

  // 加载订单详情
  loadOrderDetail: function() {
    this.setData({ loading: true });

    getOrderDetail(this.data.orderId)
      .then(res => {
        console.log('订单详情响应:', res);
        
        if (res.code === 200 && res.data) {
          const order = res.data;
          
          // 格式化订单数据
          const formattedOrder = {
            ...order,
            statusText: formatOrderStatus(order.status),
            paymentStatusText: formatPaymentStatus(order.payment_status),
            typeText: formatOrderType(order.type),
            paymentMethodText: formatPaymentMethod(order.payment_method),
            createdAtText: formatDateTime(order.created_at),
            updatedAtText: formatDateTime(order.updated_at),
            paymentTimeText: formatDateTime(order.payment_time),
            canCancel: canCancelOrder(order),
            // 格式化优惠券使用时间
            coupon_discounts: order.coupon_discounts ? order.coupon_discounts.map(coupon => ({
              ...coupon,
              used_at: formatDateTime(coupon.used_at)
            })) : []
          };

          this.setData({
            order: formattedOrder,
            loading: false
          });
        } else {
          throw new Error(res.message || '获取订单详情失败');
        }
      })
      .catch(error => {
        console.error('获取订单详情失败:', error);
        this.setData({
          loading: false
        });
        wx.showToast({
          title: error.message || '获取订单详情失败',
          icon: 'none'
        });
      });
  },

  // 取消订单
  cancelOrder: function() {
    const order = this.data.order;
    if (!order) return;
    
    wx.showModal({
      title: '确认取消',
      content: `确定要取消订单 ${order.order_no} 吗？`,
      success: (res) => {
        if (res.confirm) {
          this.performCancelOrder();
        }
      }
    });
  },

  // 执行取消订单操作
  performCancelOrder: function() {
    wx.showLoading({
      title: '取消中...'
    });

    cancelOrder(this.data.orderId)
      .then(res => {
        wx.hideLoading();
        console.log('取消订单响应:', res);
        
        if (res.code === 200) {
          wx.showToast({
            title: '订单取消成功',
            icon: 'success'
          });
          
          // 重新加载订单详情
          this.loadOrderDetail();
        } else {
          throw new Error(res.message || '取消订单失败');
        }
      })
      .catch(error => {
        wx.hideLoading();
        console.error('取消订单失败:', error);
        wx.showToast({
          title: error.message || '取消订单失败',
          icon: 'none'
        });
      });
  },

  // 登录按钮点击事件
  handleLogin: function() {
    wx.navigateTo({
      url: '/pages/phoneAuth/phoneAuth'
    });
  },

  // 返回按钮
  navigateBack: function() {
    wx.navigateBack({
      delta: 1
    });
  }
});
