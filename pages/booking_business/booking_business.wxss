/* pages/booking_business/booking_business.wxss */

/* 总体主盒子 */
.page-container {
  position: relative;
  width: 100%;
  min-height: 100vh;
  background-color: #fff;
  color: #939393;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background-color: #fff;
  color: #939393;
}

.loading {
  width: 50rpx;
  height: 50rpx;
  border: 4rpx solid #ddd;
  border-top: 4rpx solid #ff4a4a;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 30rpx;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

/* 商家信息卡片 */
.store-info {
  position: relative;
  margin-bottom: 20rpx;
}

.store-banner {
  position: relative;
  width: 100%;
  height: 140rpx;
  overflow: hidden;
}

.store-banner-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.store-banner-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(
    to bottom,
    rgba(0, 0, 0, 0.1),
    rgba(0, 0, 0, 0.2)
  );
}

.store-card {
  position: relative;
  margin: -60rpx 30rpx 0;
  padding: 80rpx 30rpx 30rpx;
  background: #fff;
  border-radius: 24rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
}

.store-avatar-container {
  position: absolute;
  top: -60rpx;
  left: 30rpx;
  width: 120rpx;
  height: 120rpx;
  border-radius: 24rpx;
  padding: 4rpx;
  background: #fff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.store-avatar {
  width: 100%;
  height: 100%;
  border-radius: 20rpx;
  object-fit: cover;
}

.store-details {
  padding-top: 8rpx;
}

.store-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16rpx;
}

.store-title {
  display: flex;
  align-items: center;
  gap: 12rpx;
}

.store-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.3;
}

.store-tag {
  font-size: 22rpx;
  color: #07c160;
  background: rgba(7, 193, 96, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-weight: 500;
}

.store-rating {
  display: flex;
  align-items: center;
  gap: 8rpx;
  background: #fff5f5;
  padding: 6rpx 12rpx;
  border-radius: 8rpx;
}

.rating-score {
  font-size: 26rpx;
  font-weight: 600;
  color: #ff4a4a;
}

.rating-count {
  font-size: 22rpx;
  color: #ff4a4a;
  opacity: 0.8;
}

/* 商家电话容器样式 */
.store-phone-container {
  margin-top: 16rpx;
  padding: 16rpx;
  background: #f8f8f8;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
}

/* 电话图标 */
.phone-icon {
  width: 32rpx;
  height: 32rpx;
  background: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjOTk5OTk5IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHBhdGggZD0iTTIyIDYuOTN2MTBjMCAxLjEtLjkgMi0yIDJINGMtMS4xIDAtMi0uOS0yLTJWNi45M2MwLS41LjQtLjkzLjktLjkzaDEuOGMuNSAwIC45LjQzLjkuOTN2My41YzAgLjI4LjIyLjUuNS41aDEyLjJjLjI4IDAgLjUtLjIyLjUtLjV2LTMuNWMwLS41LjQtLjkzLjktLjkzaDEuOGMuNSAwIC45LjQzLjkuOTN6Ii8+PC9zdmc+")
    no-repeat center;
  background-size: contain;
  margin-right: 12rpx;
  flex-shrink: 0;
}

/* 电话内容区域 */
.phone-content {
  display: flex;
  align-items: center;
  flex: 1;
}

/* 电话号码文本样式 */
.store-phone {
  font-size: 26rpx;
  color: #666;
  line-height: 1.5;
  margin-right: 10rpx;
}

/* 拨打按钮样式 */
.call-button {
  font-size: 24rpx;
  color: #666;
  background: #fff;
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
  border: 1px solid #ddd;
}

/* 主要内容区域 */
.main-content {
  position: relative;
  height: calc(100vh - 340rpx);
  overflow: hidden;
  padding-bottom: 180rpx;
}

/* 左侧分类栏主盒子 */
.left-categories {
  display: inline-block;
  width: 200rpx;
  height: 100%;
  background: #f5f5f5;
}

.category-list {
  height: 100%;
  padding: 20rpx 0;
}

/* 左侧分类栏list的item */
.category-item {
  height: 40px;
  line-height: 40px;
  padding: 5px 35rpx;
  font-size: 26rpx;
  color: #333;
}

/* 左侧分类栏list的item被选中时 */
.category-item.active {
  background: #fff;
  color: #ff4a4a;
}

.category-name {
  font-size: 26rpx;
  text-align: center;
}

/* 右侧商品列表主盒子 */
.right-products {
  display: inline-block;
  height: 100%;
  width: 550rpx;
  padding: 14rpx;
  box-sizing: border-box;
  background: #fff;
  position: absolute;
  top: 0;
  right: 0;
  padding-bottom: 180rpx;
}

.product-list {
  height: 100%;
  padding-bottom: 180rpx;
}

.product-category-title {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  padding: 20rpx 0 16rpx 0;
  margin-bottom: 8rpx;
}

/* 商品卡片 - 采用classify的横向布局 */
.product-item {
  width: 100%;
  min-height: 200rpx;
  background-color: #fff;
  margin-bottom: 14rpx;
  display: flex;
  align-items: flex-start;
  padding: 10rpx;
  box-sizing: border-box;
}

.product-image {
  width: 160rpx;
  height: 160rpx;
  border-radius: 8rpx;
  overflow: hidden;
  flex-shrink: 0;
  object-fit: cover;
}

.product-info {
  flex: 1;
  margin-left: 15rpx;
  padding: 10rpx 0;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  min-height: 160rpx;
  border-bottom: 1px solid #ddd;
}

.product-name {
  color: #000;
  margin-bottom: 8rpx;
  font-size: 28rpx;
  font-weight: 500;
  line-height: 1.4;
  word-wrap: break-word;
  word-break: break-all;
}

.product-desc {
  font-size: 22rpx;
  color: #939393;
  margin-bottom: 8rpx;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.product-meta {
  margin-bottom: 8rpx;
  flex-shrink: 0;
}

.product-sales {
  font-size: 22rpx;
  color: #939393;
  display: block;
}

.product-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: auto;
  flex-shrink: 0;
}

.product-price {
  color: #ff4a4a;
  font-size: 28rpx;
  font-weight: 700;
  flex: 1;
}

/* 商品操作按钮 - 简化样式 */
.product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
}

.action-minus,
.action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  cursor: pointer;
}

.action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

/* 空商品提示 */
.empty-products {
  padding: 100rpx 32rpx;
  text-align: center;
}

.empty-text {
  color: #939393;
  font-size: 24rpx;
  text-align: center;
}

/* 购物车 */
.cart-container {
  position: fixed;
  bottom: 40rpx;
  left: 20rpx;
  right: 20rpx;
  background: #fff;
  border-radius: 16rpx;
  z-index: 100;
  border: 1px solid #ddd;
}

.cart-expanded {
  z-index: 160;
  bottom: 0;
  left: 0;
  right: 0;
  border-radius: 16rpx 16rpx 0 0;
}

.cart-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 150;
}

.cart-header {
  display: flex;
  height: 100rpx;
  align-items: center;
  padding: 0 30rpx;
  justify-content: space-between;
  position: relative;
  background: #fff;
  border-radius: 16rpx;
  z-index: 170;
}

.cart-expanded .cart-header {
  border-radius: 16rpx 16rpx 0 0;
}

.cart-left {
  display: flex;
  align-items: center;
  flex: 1;
}

.cart-icon-container {
  position: relative;
  margin-right: 20rpx;
}

.cart-icon {
  width: 80rpx;
  height: 80rpx;
  background: #f5f5f5;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid #ddd;
}

.cart-icon.active {
  background: #ff4a4a;
  border-color: #ff4a4a;
}

.cart-icon-text {
  font-size: 28rpx;
  color: #939393;
}

.cart-icon.active .cart-icon-text {
  color: #fff;
}

.cart-count {
  position: absolute;
  top: -6rpx;
  right: -6rpx;
  background: #ff4757;
  color: #fff;
  font-size: 18rpx;
  min-width: 32rpx;
  height: 32rpx;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 6rpx;
  font-weight: 600;
}

.cart-info {
  flex: 1;
}

.cart-price {
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
}

.cart-empty {
  font-size: 24rpx;
  color: #939393;
}

.cart-submit {
  width: 180rpx;
  height: 68rpx;
  border-radius: 34rpx;
  background: #f5f5f5;
  color: #939393;
  font-size: 26rpx;
  font-weight: 600;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  border: 1px solid #ddd;
}

.cart-submit.active {
  background: #ff4a4a;
  color: #fff;
  border-color: #ff4a4a;
}

.cart-content {
  height: 400rpx;
  background: #fff;
  border-top: 1px solid #ddd;
  position: relative;
  z-index: 170;
}

.cart-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 30rpx;
  border-bottom: 1px solid #ddd;
  font-size: 26rpx;
  font-weight: 600;
  color: #333;
  background: #fff;
}

.cart-clear {
  color: #939393;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  background: #f5f5f5;
  border: 1px solid #ddd;
}

.cart-items {
  height: 320rpx;
  padding: 0 16rpx;
  background: #fff;
  overflow-y: auto;
}

.cart-item {
  display: flex;
  align-items: center;
  padding: 18rpx 0;
  border-bottom: 1px solid #ddd;
  position: relative;
  min-height: 80rpx;
  width: 100%;
  box-sizing: border-box;
}

.cart-item:last-child {
  border-bottom: none;
}

.cart-item-name {
  font-size: 24rpx;
  color: #333;
  font-weight: 500;
  line-height: 1.4;
  max-width: 445rpx;
  flex: 1;
  min-width: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin-right: 16rpx;
  flex-shrink: 1;
}

.cart-item-price-action {
  display: flex;
  align-items: center;
  flex-shrink: 0;
  width: 240rpx;
  justify-content: space-between;
  position: relative;
}

.cart-item-price {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 600;
  width: 80rpx;
  text-align: right;
  flex-shrink: 0;
}

/* 购物车中product-action的样式 */
.cart-item .product-action {
  display: flex;
  align-items: center;
  background: #f5f5f5;
  border-radius: 20rpx;
  padding: 4rpx;
  gap: 4rpx;
  border: 1px solid #ddd;
  width: 140rpx;
  flex-shrink: 0;
  justify-content: space-between;
}

.cart-item .action-minus,
.cart-item .action-plus {
  width: 44rpx;
  height: 44rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: 600;
  cursor: pointer;
  flex-shrink: 0;
}

.cart-item .action-minus {
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.cart-item .action-plus {
  background: #ff4a4a;
  color: #fff;
  border: 1px solid #ff4a4a;
}

.cart-item .action-count {
  width: 48rpx;
  text-align: center;
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
  flex-shrink: 0;
}

/* 弹窗样式 */
.dish-detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 200;
}

.modal-mask {
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
}

.modal-content {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top-left-radius: 20rpx;
  border-top-right-radius: 20rpx;
  overflow: hidden;
}

.modal-close {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(0, 0, 0, 0.6);
  border-radius: 50%;
  z-index: 10;
}

.modal-image {
  width: 100%;
  height: 360rpx;
  object-fit: cover;
}

.modal-info {
  padding: 28rpx;
  background: #fff;
}

.modal-name {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.modal-desc {
  font-size: 26rpx;
  color: #939393;
  margin-bottom: 24rpx;
  line-height: 1.5;
}

.modal-price-action {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-top: 20rpx;
  border-top: 1px solid #ddd;
}

.modal-price {
  font-size: 36rpx;
  color: #ff4a4a;
  font-weight: 600;
}

/* 结算弹窗优化 */
.checkout-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 300;
  display: flex;
  justify-content: center;
  align-items: center;
  background: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(5px);
}

.checkout-content {
  background: #fff;
  border-radius: 24rpx;
  width: 90%;
  max-height: 88%;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  box-shadow: 0 10rpx 40rpx rgba(0, 0, 0, 0.2);
  animation: slide-up 0.3s ease;
}

@keyframes slide-up {
  from {
    opacity: 0;
    transform: translateY(50rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.checkout-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.2);
  position: relative;
}

.checkout-title {
  font-size: 34rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-align: left;
}

.checkout-header .modal-close {
  position: static;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #fff;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  margin-left: 20rpx;
}

.checkout-scroll {
  flex: 1;
  overflow-y: auto;
  max-height: calc(85vh - 180rpx);
  padding: 10rpx 0;
}

/* 商品列表美化 - 修复滑块问题 */
.checkout-cart {
  margin: 20rpx 30rpx;
  border-radius: 16rpx;
  overflow: hidden;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
  background: #fff;
}

.cart-label {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  color: #333;
  width: 100%;
  padding: 20rpx 20rpx 0;
}

.checkout-items {
  max-height: 320rpx;
  padding: 0 20rpx 20rpx;
  background: #fff;
  overflow-y: auto;
  width: 100%;
  box-sizing: border-box;
}

/* 确保滚动区域有足够的内边距，防止出现多余滑块 */
.checkout-items::-webkit-scrollbar {
  display: none; /* 隐藏滚动条但保留滚动功能 */
}

.checkout-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx 0;
  font-size: 28rpx;
  border-bottom: 1px solid #f0f0f0;
}

.checkout-item:last-child {
  border-bottom: none;
}

.checkout-item-name {
  flex: 1;
  font-weight: 500;
  color: #333;
}

.checkout-item-count {
  margin: 0 16rpx;
  color: #666;
  font-weight: 500;
  background: #f5f5f5;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.checkout-item-price {
  color: #ff4a4a;
  font-weight: 600;
}

/* 预约信息区域美化 */
.checkout-datetime,
.checkout-people,
.checkout-name,
.checkout-contact,
.checkout-remark {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  border-radius: 16rpx;
  background: #fff;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.datetime-label,
.people-label,
.name-label,
.contact-label,
.remark-label {
  font-size: 30rpx;
  font-weight: 600;
  margin-bottom: 16rpx;
  display: block;
  color: #333;
}

.required {
  color: #ff4a4a;
  font-weight: bold;
  margin-left: 4rpx;
  font-size: 26rpx;
}

.optional {
  color: #999;
  font-size: 24rpx;
  margin-left: 8rpx;
  font-weight: normal;
}

/* 日期时间选择器样式统一 */
.picker-view {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #eaeaea;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  color: #333;
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: 88rpx;
  display: flex;
  align-items: center;
  position: relative;
}

.picker-view::after {
  content: "▼";
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 20rpx;
  color: #999;
  pointer-events: none;
}

/* 输入框美化 */
.people-input,
.name-input,
.contact-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #eaeaea;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  color: #333;
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: 88rpx;
}

.remark-input {
  width: 100%;
  padding: 20rpx;
  border: 1px solid #eaeaea;
  border-radius: 12rpx;
  font-size: 28rpx;
  background: #f9f9f9;
  color: #333;
  transition: all 0.3s ease;
  box-sizing: border-box;
  height: 180rpx;
}

.people-input:focus,
.name-input:focus,
.contact-input:focus,
.remark-input:focus {
  border-color: #ff4a4a;
  background: #fff;
  box-shadow: 0 0 0 3px rgba(255, 74, 74, 0.1);
}

/* 最近姓名+电话对标签样式 */
.recent-names {
  margin-top: 16rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.recent-name-item {
  padding: 16rpx 20rpx;
  background: #f0f8ff;
  border: 1px solid #e1f0ff;
  border-radius: 20rpx;
  font-size: 26rpx;
  color: #1890ff;
  cursor: pointer;
  transition: all 0.3s ease;
  user-select: none;
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  min-width: 120rpx;
}

.recent-name-item:hover {
  background: #e6f7ff;
  border-color: #91d5ff;
  transform: translateY(-2rpx);
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.15);
}

.recent-name-item:active {
  transform: translateY(0);
  box-shadow: 0 2rpx 6rpx rgba(24, 144, 255, 0.1);
}

.name-text {
  font-weight: 600;
  color: #1890ff;
}

.phone-text {
  font-size: 24rpx;
  color: #666;
  font-weight: normal;
}

/* 总价美化 */
.checkout-total {
  margin: 20rpx 30rpx;
  padding: 20rpx;
  text-align: right;
  font-size: 34rpx;
  font-weight: 600;
  border-radius: 16rpx;
  background: #fff;
  color: #ff4a4a;
  box-shadow: 0 4rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 提交按钮美化 */
.checkout-submit {
  height: 100rpx;
  margin-top: 20rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  cursor: pointer;
  border: none;
  letter-spacing: 4rpx;
  transition: all 0.3s ease;
  flex-shrink: 0;
  position: relative;
  overflow: hidden;
}

.checkout-submit:active {
  opacity: 0.9;
  transform: scale(0.98);
}

.checkout-submit::after {
  content: "";
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(
    90deg,
    transparent,
    rgba(255, 255, 255, 0.2),
    transparent
  );
  animation: shine 2s infinite;
}

@keyframes shine {
  to {
    left: 100%;
  }
}

/* 订单成功提示 */
.order-success {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: #fff;
  z-index: 400;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.success-icon {
  width: 100rpx;
  height: 100rpx;
  background: #52c41a;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  color: #fff;
  margin-bottom: 24rpx;
}

.success-text {
  font-size: 32rpx;
  font-weight: 600;
  margin-bottom: 12rpx;
  color: #333;
}

.success-order-id {
  font-size: 24rpx;
  color: #939393;
  margin-bottom: 48rpx;
  background: #f5f5f5;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
}

.success-actions {
  display: flex;
  gap: 16rpx;
}

.success-btn {
  width: 200rpx;
  height: 72rpx;
  border-radius: 36rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 26rpx;
  font-weight: 500;
  background: #f5f5f5;
  color: #333;
  border: 1px solid #ddd;
}

.success-btn.primary {
  background: #ff4a4a;
  color: #fff;
  border-color: #ff4a4a;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .checkout-content {
    width: 90%;
  }

  .success-actions {
    flex-direction: column;
    gap: 12rpx;
  }

  .success-btn {
    width: 240rpx;
  }
}

/* 修改关闭按钮位置 */
.checkout-content .modal-close {
  position: absolute;
  top: 10rpx;
  right: 10rpx;
  background: rgba(0, 0, 0, 0.2);
  color: #fff;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
}

/* 支付弹窗样式 */
.payment-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 1000;
}

.payment-container {
  background: #fff;
  border-radius: 12px;
  width: 80%;
  padding: 20px;
}

.payment-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  font-size: 32rpx;
  color: #333;
  font-weight: 500;
}

.payment-amount {
  font-size: 48rpx;
  font-weight: bold;
  text-align: center;
  margin: 30rpx 0;
  color: rgb(16, 15, 15);
  text-shadow: 0 2rpx 4rpx rgba(255, 74, 74, 0.1);
}

.payment-methods {
  margin: 30rpx 0;
}

.payment-methods text {
  font-size: 30rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 20rpx;
  display: block;
}

/* 支付方式选项样式优化 */
.payment-method {
  display: flex;
  align-items: center;
  padding: 20rpx 30rpx;
  border: 1px solid #eee;
  border-radius: 8px;
  margin-bottom: 20rpx;
  position: relative;
  justify-content: space-between;
}

/* 支付方式左侧内容 */
.payment-method-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0;
}

.method-icon {
  width: 48rpx;
  height: 48rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
}

.payment-method-text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-right: 20rpx;
  flex-shrink: 0;
}

/* 余额信息样式 */
.balance-info {
  display: flex;
  align-items: center;
  flex-shrink: 0;
}

.balance-text {
  font-size: 24rpx;
  color: #666;
  margin-right: 8rpx;
}

.balance-amount {
  font-size: 24rpx;
  color: #ff4a4a;
  font-weight: 500;
}

/* 单选按钮容器 */
.radio-container {
  display: flex;
  align-items: center;
  margin-left: 20rpx;
  flex-shrink: 0;
}

/* 单选按钮样式 */
.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

/* 余额不足提示 */
.insufficient-tip {
  position: absolute;
  right: 80rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #ff4d4f;
  background: rgba(255, 77, 79, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 6rpx;
  white-space: nowrap;
}

/* 选中状态样式 */
.payment-method.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

/* 禁用状态样式 */
.payment-method.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 支付方式选中状态样式 */
.payment-method.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

/* 单选按钮样式 */
.radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.payment-method.selected .radio-btn {
  background: #07c160;
  border-color: #07c160;
}

.payment-method.selected .radio-btn::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

/* 确认支付按钮样式 */
.confirm-payment-btn {
  background: #07c160;
  color: #fff;
  border: none;
  border-radius: 8px;
  padding: 24rpx;
  width: 100%;
  font-size: 32rpx;
  font-weight: 500;
  margin-top: 30rpx;
  transition: all 0.3s ease;
}

.confirm-payment-btn:active {
  opacity: 0.9;
  transform: scale(0.98);
}

/* 禁用状态的支付方式 */
.payment-method.disabled {
  opacity: 0.6;
  pointer-events: none;
}

.payment-method.disabled .radio-btn {
  border-color: #ddd;
  background: #f5f5f5;
}

.deposit-tip {
  padding: 10px 20px;
  margin: 10px 0;
  font-size: 14px;
  color: #ff6b00;
  line-height: 1.5;
  text-align: left;
  background-color: #f8f8f8;
  border-radius: 4px;
}

.enterprise-list {
  margin-top: 20rpx;
  padding: 0 20rpx;
}

.enterprise-label {
  font-size: 28rpx;
  color: #333;
  margin-bottom: 16rpx;
  display: block;
}

.enterprise-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border: 1px solid #eee;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  background: #fff;
}

.enterprise-item.selected {
  border-color: #07c160;
  background: rgba(7, 193, 96, 0.05);
}

.enterprise-name {
  font-size: 28rpx;
  color: #333;
}

.enterprise-item .radio-btn {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #ddd;
  border-radius: 50%;
  position: relative;
}

.enterprise-item.selected .radio-btn {
  border-color: #07c160;
  background: #07c160;
}

.enterprise-item.selected .radio-btn::after {
  content: "";
  position: absolute;
  width: 20rpx;
  height: 20rpx;
  background: #fff;
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.no-enterprise {
  text-align: center;
  padding: 30rpx 0;
  color: #999;
  font-size: 28rpx;
}

.cancel-notice {
  margin: 20rpx 0;
  padding: 20rpx;
  background-color: #f8f8f8;
  border-radius: 8rpx;
  border-left: 4rpx solid #ffa500;
}

.cancel-notice-title {
  font-size: 28rpx;
  color: #333;
  font-weight: bold;
  margin-bottom: 10rpx;
}

.cancel-notice-content {
  padding: 20rpx;
  background-color: #fff8f0;
  border-radius: 8rpx;
  box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.1);
  position: relative;
}

/* 使用伪元素创建边框，确保边框圆角正确显示 */
.cancel-notice-content::after {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border: 1rpx solid #ffd591;
  border-radius: 8rpx;
  pointer-events: none;
  z-index: 1;
}

.cancel-notice-item {
  font-size: 26rpx;
  color: #f44336;
  line-height: 1.6;
  margin-bottom: 10rpx;
}

.cancel-notice-item:last-child {
  margin-bottom: 0;
}

/* 地址文本样式 - 修改字体大小 */
.store-address {
  font-size: 26rpx; /* 原来是26rpx，改小了 */
  color: #666;
  line-height: 1.5;
  flex: 1;
}

/* 可用时段弹窗样式 - 美化版 */
.available-slots-dialog {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.6);
  backdrop-filter: blur(3px);
}

.dialog-container {
  position: relative;
  width: 85%;
  max-height: 75%;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  z-index: 10000;
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
  animation: slideUp 0.3s ease;
}

@keyframes slideUp {
  from {
    transform: translateY(50rpx);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

/* 弹窗标题栏 */
.dialog-header {
  padding: 30rpx;
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(255, 255, 255, 0.2);
}

.dialog-title {
  font-size: 34rpx;
  font-weight: 600;
  letter-spacing: 2rpx;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dialog-close {
  width: 50rpx;
  height: 50rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  background: rgba(255, 255, 255, 0.2);
  border-radius: 50%;
  transition: all 0.2s ease;
}

.dialog-close:active {
  background: rgba(255, 255, 255, 0.4);
  transform: scale(0.95);
}

/* 弹窗内容区 */
.dialog-content {
  flex: 1;
  padding: 30rpx;
  overflow-y: auto;
}

/* 提示信息样式 */
.dialog-message {
  display: flex;
  align-items: center;
  padding: 20rpx;
  background-color: #fff8f0;
  border-radius: 12rpx;
  margin-bottom: 24rpx;
  border-left: 8rpx solid #ff9800;
}

.message-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #ff9800;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  margin-right: 16rpx;
  flex-shrink: 0;
}

.message-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  flex: 1;
}

/* 时段列表样式 */
.slots-list {
  max-height: 500rpx;
  margin: 20rpx 0;
  border-radius: 12rpx;
  overflow: hidden;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  border: 1px solid #eee;
}

.slot-item-header {
  display: flex;
  justify-content: space-between;
  padding: 20rpx 30rpx;
  background-color: #f5f5f5;
  font-weight: 600;
  border-bottom: 1px solid #eee;
}

.slot-header-date,
.slot-header-time {
  font-size: 28rpx;
  color: #333;
}

.slot-item.info-only {
  display: flex;
  justify-content: space-between;
  padding: 24rpx 30rpx;
  border-bottom: 1px solid #f0f0f0;
  transition: background-color 0.2s;
}

.slot-item.info-only:last-child {
  border-bottom: none;
}

.slot-item.info-only:nth-child(odd) {
  background-color: #fafafa;
}

.slot-date,
.slot-time {
  display: flex;
  align-items: center;
}

.date-icon,
.time-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 10rpx;
  background-size: contain;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.date-icon {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMzMzIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PHJlY3QgeD0iMyIgeT0iNCIgd2lkdGg9IjE4IiBoZWlnaHQ9IjE4IiByeD0iMiIgcnk9IjIiPjwvcmVjdD48bGluZSB4MT0iMTYiIHkxPSIyIiB4Mj0iMTYiIHkyPSI2Ij48L2xpbmU+PGxpbmUgeDE9IjgiIHkxPSIyIiB4Mj0iOCIgeTI9IjYiPjwvbGluZT48bGluZSB4MT0iMyIgeTE9IjEwIiB4Mj0iMjEiIHkyPSIxMCI+PC9saW5lPjwvc3ZnPg==");
}

.time-icon {
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjZmY0YTRhIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxwb2x5bGluZSBwb2ludHM9IjEyIDYgMTIgMTIgMTYgMTQiPjwvcG9seWxpbmU+PC9zdmc+");
}

.slot-date text {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.slot-time text {
  font-size: 28rpx;
  color: #ff4a4a;
  font-weight: 500;
}

/* 空状态样式 */
.slots-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  color: #999;
}

.empty-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 16rpx;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjOTk5IiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxsaW5lIHgxPSIxMiIgeTE9IjgiIHgyPSIxMiIgeTI9IjEyIj48L2xpbmU+PGxpbmUgeDE9IjEyIiB5MT0iMTYiIHgyPSIxNiI+PC9saW5lPjxsaW5lIHgxPSIxMiIgeTE9IjgiIHgyPSIxMi4wMSIgeTI9IjgiPjwvbGluZT48L3N2Zz4=");
  background-size: contain;
  background-repeat: no-repeat;
}

.slots-empty text {
  font-size: 28rpx;
}

/* 底部提示样式 */
.slots-note {
  display: flex;
  align-items: center;
  margin-top: 20rpx;
  padding: 16rpx;
  background-color: #f0f9ff;
  border-radius: 8rpx;
  border: 1px solid #d0e8ff;
}

.note-icon {
  width: 32rpx;
  height: 32rpx;
  margin-right: 12rpx;
  background-image: url("data:image/svg+xml;base64,PHN2ZyB4bWxucz0iaHR0cDovL3d3dy53My5vcmcvMjAwMC9zdmciIHZpZXdCb3g9IjAgMCAyNCAyNCIgZmlsbD0ibm9uZSIgc3Ryb2tlPSIjMTA5OWZmIiBzdHJva2Utd2lkdGg9IjIiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCI+PGNpcmNsZSBjeD0iMTIiIGN5PSIxMiIgcj0iMTAiPjwvY2lyY2xlPjxsaW5lIHgxPSIxMiIgeTE9IjE2IiB4Mj0iMTIiIHkyPSIxMiI+PC9saW5lPjxsaW5lIHgxPSIxMiIgeTE9IjgiIHgyPSIxMi4wMSIgeTI9IjgiPjwvbGluZT48L3N2Zz4=");
  background-size: contain;
  background-repeat: no-repeat;
  flex-shrink: 0;
}

.slots-note text {
  font-size: 26rpx;
  color: #0076cc;
  line-height: 1.5;
}

/* 弹窗底部按钮 */
.dialog-footer {
  padding: 20rpx 30rpx 30rpx;
  display: flex;
  justify-content: center;
}

.dialog-btn {
  background: linear-gradient(135deg, #ff4a4a, #ff7676);
  color: #fff;
  font-size: 30rpx;
  font-weight: 600;
  padding: 20rpx 0;
  border-radius: 40rpx;
  border: none;
  width: 80%;
  text-align: center;
  box-shadow: 0 4rpx 12rpx rgba(255, 74, 74, 0.3);
  letter-spacing: 2rpx;
  transition: all 0.2s ease;
}

.dialog-btn:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(255, 74, 74, 0.2);
}

/* 优惠券选择区域 */
.coupon-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0 20rpx 16rpx;
}

.coupon-label {
  color: #333;
  font-size: 28rpx;
  font-weight: 500;
}

.coupon-content {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.coupon-text {
  color: #999;
  font-size: 28rpx;
  margin-right: 10rpx;
}

.coupon-text.selected {
  color: #4080ff;
}

.coupon-arrow {
  color: #ccc;
  font-size: 24rpx;
}

.coupon-discount {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx 20rpx 0;
  margin-bottom: 20rpx;
}

.discount-label {
  color: #666;
  font-size: 26rpx;
}

.discount-amount {
  color: #ff4444;
  font-size: 26rpx;
  font-weight: 500;
}

.clear-all-coupons {
  padding: 16rpx 20rpx;
  text-align: center;
  border-top: 1rpx solid #f0f0f0;
  background: #fafafa;
  margin: 0 -20rpx;
}

.clear-all-text {
  color: #ff6b6b;
  font-size: 26rpx;
}

/* 已选择的优惠券详情 */
.selected-coupons {
  background: #f8f9fa;
  border-top: 1rpx solid #eee;
  margin: 0 -20rpx;
  padding: 0 20rpx;
}

.coupon-item-detail {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.coupon-item-detail:last-child {
  border-bottom: none;
}

.coupon-info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.coupon-name {
  color: #333;
  font-size: 26rpx;
  font-weight: 500;
  margin-bottom: 8rpx;
}

.coupon-type {
  color: #666;
  font-size: 22rpx;
}

.coupon-actions {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.coupon-discount-amount {
  color: #ff6b6b;
  font-size: 26rpx;
  font-weight: 600;
}

.cancel-coupon-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40rpx;
  height: 40rpx;
  background: #f5f5f5;
  border-radius: 50%;
  border: 1rpx solid #ddd;
}

.cancel-icon {
  color: #999;
  font-size: 24rpx;
  font-weight: bold;
  line-height: 1;
}
