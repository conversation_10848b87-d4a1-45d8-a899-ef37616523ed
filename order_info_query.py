#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
订单信息查询脚本
通过输入 order_id，打印订单及其相关信息的所有属性值
"""

import sys
import os
from datetime import datetime
from typing import Optional
from sqlalchemy.orm import Session, selectinload

# 添加项目根目录到 Python 路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.order import Order, OrderItem
from app.models.reservation import ReservationRequest
from app.models.coupon import CouponUsageRecord


def print_separator(title: str = "", width: int = 80):
    """打印分隔线"""
    if title:
        print(f"\n{'=' * width}")
        print(f"{title:^{width}}")
        print('=' * width)
    else:
        print('-' * width)


def print_model_attributes(obj, title: str, exclude_fields: set = None):
    """打印模型对象的所有属性，包括继承的属性"""
    if obj is None:
        print(f"\n{title}: None")
        return
    
    exclude_fields = exclude_fields or set()
    print(f"\n{title}:")
    print_separator("", 60)
    
    # 获取所有属性，包括继承的属性
    all_columns = []
    
    # 获取当前类及其所有父类的列
    for cls in obj.__class__.__mro__:
        if hasattr(cls, '__table__'):
            for column in cls.__table__.columns:
                # 避免重复添加相同的列
                if not any(existing_col.name == column.name for existing_col in all_columns):
                    all_columns.append(column)
    
    # 按列名排序，便于查看
    all_columns.sort(key=lambda col: col.name)
    
    # 显示对象类型信息
    print(f"  {'对象类型':<25}: {obj.__class__.__name__}")
    print(f"  {'表名':<25}: {obj.__table__.name}")
    print_separator("", 60)
    
    # 打印所有属性
    for column in all_columns:
        if column.name in exclude_fields:
            continue
            
        value = getattr(obj, column.name, None)
        
        # 格式化特殊类型的值
        if isinstance(value, datetime):
            value_str = value.strftime("%Y-%m-%d %H:%M:%S") if value else "None"
        elif isinstance(value, (int, float)) and value is not None:
            value_str = str(value)
        elif value is None:
            value_str = "None"
        else:
            value_str = str(value)
            
        # 标记字段来源表
        table_name = column.table.name
        field_info = f"{column.name} ({table_name})" if table_name != obj.__table__.name else column.name
        print(f"  {field_info:<35}: {value_str}")


def print_list_attributes(obj_list, title: str, exclude_fields: set = None):
    """打印对象列表的所有属性，包括继承的属性"""
    if not obj_list:
        print(f"\n{title}: 无数据")
        return
        
    print(f"\n{title} (共 {len(obj_list)} 条):")
    
    for i, obj in enumerate(obj_list, 1):
        print(f"\n  第 {i} 条记录 ({obj.__class__.__name__}):")
        print_separator("", 50)
        
        exclude_fields = exclude_fields or set()
        
        # 获取所有属性，包括继承的属性
        all_columns = []
        
        # 获取当前类及其所有父类的列
        for cls in obj.__class__.__mro__:
            if hasattr(cls, '__table__'):
                for column in cls.__table__.columns:
                    # 避免重复添加相同的列
                    if not any(existing_col.name == column.name for existing_col in all_columns):
                        all_columns.append(column)
        
        # 按列名排序，便于查看
        all_columns.sort(key=lambda col: col.name)
        
        # 打印所有属性
        for column in all_columns:
            if column.name in exclude_fields:
                continue
                
            value = getattr(obj, column.name, None)
            
            # 格式化特殊类型的值
            if isinstance(value, datetime):
                value_str = value.strftime("%Y-%m-%d %H:%M:%S") if value else "None"
            elif isinstance(value, (int, float)) and value is not None:
                value_str = str(value)
            elif value is None:
                value_str = "None"
            else:
                value_str = str(value)
                
            # 标记字段来源表
            table_name = column.table.name
            field_info = f"{column.name} ({table_name})" if table_name != obj.__table__.name else column.name
            print(f"    {field_info:<33}: {value_str}")


def query_order_info(order_id: int):
    """查询订单信息"""
    db: Session = SessionLocal()
    
    try:
        print_separator(f"订单信息查询 - Order ID: {order_id}")
        
        # 查询订单及其关联数据
        order = db.query(Order).options(
            selectinload(Order.items),
            selectinload(Order.reservation_requests),
            selectinload(Order.coupon_usage_records)
        ).filter(Order.id == order_id).first()
        
        if not order:
            print(f"\n❌ 未找到 ID 为 {order_id} 的订单")
            return
            
        # 1. 打印订单基本信息
        print_model_attributes(order, "📋 订单基本信息")
        
        # 2. 打印订单项信息
        print_list_attributes(order.items, "📦 订单项信息")
        
        # 3. 打印预约请求信息
        print_list_attributes(order.reservation_requests, "📅 预约请求信息")
        
        # 4. 打印优惠券使用记录
        print_list_attributes(order.coupon_usage_records, "🎫 优惠券使用记录")
        
        # 5. 查询订单项相关的预约请求
        order_item_reservations = []
        for item in order.items:
            item_reservations = db.query(ReservationRequest).filter(
                ReservationRequest.order_item_id == item.id
            ).all()
            order_item_reservations.extend(item_reservations)
            
        if order_item_reservations:
            print_list_attributes(order_item_reservations, "📅 订单项关联的预约请求")
        
        # 6. 查询订单项相关的优惠券使用记录
        order_item_coupons = []
        for item in order.items:
            item_coupons = db.query(CouponUsageRecord).filter(
                CouponUsageRecord.order_item_id == item.id
            ).all()
            order_item_coupons.extend(item_coupons)
            
        if order_item_coupons:
            print_list_attributes(order_item_coupons, "🎫 订单项关联的优惠券使用记录")
            
        print_separator("查询完成")
        
    except Exception as e:
        print(f"\n❌ 查询过程中出现错误: {str(e)}")
        import traceback
        traceback.print_exc()
        
    finally:
        db.close()


def main():
    """主函数"""
    print("🔍 订单信息查询工具")
    print("=" * 50)
    
    if len(sys.argv) > 1:
        # 从命令行参数获取订单ID
        try:
            order_id = int(sys.argv[1])
            query_order_info(order_id)
        except ValueError:
            print("❌ 请输入有效的订单ID（数字）")
            sys.exit(1)
    else:
        # 交互式输入
        while True:
            try:
                order_id_input = input("\n请输入订单ID（输入 'q' 退出）: ").strip()
                
                if order_id_input.lower() == 'q':
                    print("👋 再见！")
                    break
                    
                order_id = int(order_id_input)
                query_order_info(order_id)
                
            except ValueError:
                print("❌ 请输入有效的订单ID（数字）")
            except KeyboardInterrupt:
                print("\n\n👋 再见！")
                break
            except Exception as e:
                print(f"❌ 发生错误: {str(e)}")


if __name__ == "__main__":
    main()
