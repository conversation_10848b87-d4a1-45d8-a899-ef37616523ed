from app.service.order_status import order_status_service
from app.db.session import SessionLocal
from app.dao.order import order_dao

# Create database session
session = SessionLocal()

try:
    # Check single order (uncomment to test specific order)
    # order = order_dao.get(session, order_id=1)
    # if order:
    #     result = order_status_service.check(order)
    #     print(f"Order {result.order_id} (type: {result.order_type.value}) valid: {result.is_valid}")
    #     print(f"Order status: {result.order_status.value}")
    #     print(f"Order items:")
    #     for item in result.order_items:
    #         print(f"  - Item {item['order_item_id']}: {item['status'].value}")
    # else:
    #     print("Order not found")

    # Check all orders
    print("Checking all orders...")
    all_passed, failed_orders = order_status_service.check_all(session)
    
    print(f"All orders passed validation: {all_passed}")
    print(f"Number of failed orders: {len(failed_orders)}")
    
    if not all_passed:
        print("\nFailed orders:")
        for failed_order in failed_orders:
            print(f"- Order {failed_order.order_id} (type: {failed_order.order_type.value}) (status: {failed_order.order_status.value})")
            for item in failed_order.order_items:
                print(f"  Order item {item['order_item_id']} (status: {item['status'].value})")
            print("")

    else:
        print("All orders have valid status!")
        
finally:
    session.close()