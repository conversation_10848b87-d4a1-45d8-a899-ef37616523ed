import { loginRequest } from './index';

/**
 * 获取文章详情
 * @param {number} articleId 文章ID
 * @param {number} sharerId 分享人ID (可选)
 * @returns {Promise} 返回文章详情数据
 */
export const getArticleDetail = async (articleId, sharerId = null) => {
  try {
    const token = wx.getStorageSync('token');
    const params = {};
    
    // 如果有分享人ID，添加到请求参数中
    if (sharerId) {
      params.sharerId = sharerId;
    }
    
    const response = await loginRequest.get({
      url: `/article/published/${articleId}`,
      data: params,
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('获取文章详情失败:', error);
    throw error;
  }
};

/**
 * 获取已发布的文章列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文章列表数据
 */
export const getPublishedArticles = async (params = {}) => {
  try {
    const token = wx.getStorageSync('token');
    const response = await loginRequest.get({
      url: '/article/published/articles',
      data: params,
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('获取已发布文章列表失败:', error);
    throw error;
  }
};

/**
 * 获取文章列表
 * @param {Object} params 查询参数
 * @returns {Promise} 返回文章列表数据
 */
export const getArticleList = async (params = {}) => {
  try {
    const token = wx.getStorageSync('token');
    const response = await loginRequest.get({
      url: '/articles',
      data: params,
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('获取文章列表失败:', error);
    throw error;
  }
};

/**
 * 搜索文章
 * @param {string} keyword 搜索关键词
 * @param {Object} params 其他查询参数
 * @returns {Promise} 返回搜索结果
 */
export const searchArticles = async (keyword, params = {}) => {
  try {
    const token = wx.getStorageSync('token');
    const response = await loginRequest.get({
      url: '/articles/search',
      data: {
        keyword,
        ...params
      },
      header: {
        'token': token
      }
    });
    return response;
  } catch (error) {
    console.error('搜索文章失败:', error);
    throw error;
  }
}; 