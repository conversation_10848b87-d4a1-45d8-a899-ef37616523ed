# 优惠券使用记录回退脚本使用说明

## 功能介绍

`coupon_rollback.py` 是一个用于回退优惠券使用记录的脚本，支持：

- 🎫 **单个记录回退**：根据 `coupon_usage_record_id` 回退单个优惠券使用记录
- 📦 **批量订单回退**：回退指定订单的所有优惠券使用记录
- 🔍 **详细信息显示**：显示记录的完整信息和关联数据
- ✅ **安全确认机制**：操作前需要用户确认，防止误操作

## 回退操作内容

当执行回退操作时，脚本会将优惠券使用记录的以下字段进行修改：

| 字段 | 原值 | 回退后值 | 说明 |
|------|------|----------|------|
| `status` | `USED` | `VALID` | 状态从已使用改为有效 |
| `order_id` | 订单ID | `NULL` | 清空关联订单ID |
| `order_item_id` | 订单项ID | `NULL` | 清空关联订单项ID |
| `discount_amount` | 优惠金额 | `0.0` | 重置优惠金额为0 |
| `used_at` | 使用时间 | `NULL` | 清空使用时间 |

## 使用方法

### 方法1：命令行参数 - 单个记录回退

```bash
# 回退ID为123的优惠券使用记录
python coupon_rollback.py 123
```

### 方法2：命令行参数 - 批量订单回退

```bash
# 回退订单456的所有优惠券使用记录
python coupon_rollback.py --order 456
```

### 方法3：交互式模式

```bash
# 启动交互模式
python coupon_rollback.py

# 然后根据提示选择操作模式
选择操作模式:
1. 单个记录回退
2. 按订单批量回退
q. 退出

请选择操作模式 (1/2/q): 1
请输入优惠券使用记录ID: 123
```

### 方法4：使用虚拟环境

```bash
# 如果使用poetry
poetry run python coupon_rollback.py 123

# 或者激活虚拟环境后运行
source venv/bin/activate
python coupon_rollback.py 123
```

## 输出示例

### 单个记录回退示例

```
🎫 优惠券使用记录回退工具
==================================================

================================================================================
                    优惠券使用记录回退 - Record ID: 123
================================================================================

📋 优惠券使用记录信息:
============================================================
  记录ID              : 123
  优惠券ID            : 456
  优惠券批次ID        : 789
  用户ID              : 101
  订单ID              : 202
  订单项ID            : 303
  优惠金额            : 10.0
  使用状态            : used
  分发渠道            : system
  使用时间            : 2023-12-01 10:30:00
  创建时间            : 2023-12-01 09:00:00
  更新时间            : 2023-12-01 10:30:00
  关联优惠券名称      : 新用户专享券
  关联用户名          : user123

🔄 即将执行的回退操作:
  • 状态: used → valid
  • 订单ID: 清空
  • 订单项ID: 清空
  • 优惠金额: 重置为 0.0
  • 使用时间: 清空

确认执行回退操作？ (y/n): y

🔄 正在执行回退操作...
✅ 回退操作成功完成！

📊 回退操作详情:
============================================================
  字段           原值                新值
============================================================
  状态           used                valid
  订单ID         202                 None
  订单项ID       303                 None
  优惠金额       10.0                0.0
  使用时间       2023-12-01 10:30:00 None
```

### 批量订单回退示例

```
================================================================================
                      批量回退订单优惠券 - Order ID: 456
================================================================================

✅ 优惠券回退成功，共回退 3 条记录
```

## 安全特性

### 1. 状态检查
- 脚本会检查记录的当前状态
- 只有 `USED` 状态的记录才建议回退
- 其他状态会显示警告并询问是否继续

### 2. 用户确认
- 所有回退操作都需要用户明确确认
- 显示详细的操作内容让用户了解将要执行的操作
- 支持取消操作

### 3. 事务安全
- 使用数据库事务确保操作的原子性
- 操作失败时自动回滚，不会产生不一致的数据

### 4. 详细日志
- 显示操作前后的完整数据对比
- 记录所有关键操作步骤

## 使用场景

### 1. 订单取消后的优惠券回退
```bash
# 当订单被取消时，回退该订单使用的所有优惠券
python coupon_rollback.py --order 12345
```

### 2. 错误使用的优惠券回退
```bash
# 当优惠券被错误使用时，回退特定记录
python coupon_rollback.py 67890
```

### 3. 系统维护时的批量回退
```bash
# 交互式模式便于批量处理多个记录
python coupon_rollback.py
```

## 注意事项

### ⚠️ 重要警告

1. **数据一致性**: 回退优惠券后，请确保相关的订单金额、支付状态等数据也相应调整
2. **业务逻辑**: 回退操作只是技术层面的状态修改，可能需要配合其他业务逻辑调整
3. **权限控制**: 该脚本具有直接修改数据库的权限，请谨慎使用

### 🔧 技术要求

1. **数据库连接**: 需要有数据库的读写权限
2. **环境变量**: 确保正确配置了数据库连接参数
3. **依赖包**: 确保安装了所有必要的Python依赖

## 故障排除

### 常见问题

**Q: 提示 "记录不存在"**  
A: 检查输入的记录ID是否正确，或该记录是否真实存在

**Q: 回退操作失败**  
A: 检查数据库连接和权限，查看错误日志了解具体原因

**Q: 状态警告**  
A: 只有 `USED` 状态的记录才建议回退，其他状态请谨慎操作

## 扩展功能

脚本支持轻松扩展：
- 添加更多的回退条件检查
- 支持按用户批量回退
- 添加回退历史记录
- 集成到订单管理系统中

## 相关工具

- `order_info_query.py`: 查询订单详细信息
- `OrderCancelService.rollback_coupon_usage()`: 程序化的优惠券回退方法
